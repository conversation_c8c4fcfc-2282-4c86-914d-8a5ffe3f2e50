<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/build" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/flutter_secure_storage/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/flutter_secure_storage/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/flutter_secure_storage/build" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/flutter_secure_storage/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/flutter_secure_storage/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/flutter_secure_storage/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/local_auth_darwin/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/local_auth_darwin/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/local_auth_darwin/build" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/local_auth_darwin/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/local_auth_darwin/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/local_auth_darwin/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/path_provider_foundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/path_provider_foundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/path_provider_foundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/path_provider_foundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/path_provider_foundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Cards_Management/cards_management/ios/.symlinks/plugins/path_provider_foundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/build" />
    </content>
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Dart SDK" level="project" />
    <orderEntry type="library" name="Dart Packages" level="project" />
  </component>
</module>