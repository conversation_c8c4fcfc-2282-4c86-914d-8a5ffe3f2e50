{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "device_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-06-09 11:35:30.998993", "version": "3.29.2", "swift_package_manager_enabled": {"ios": false, "macos": false}}