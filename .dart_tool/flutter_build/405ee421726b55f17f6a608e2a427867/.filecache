{"version": 2, "files": [{"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/stream_container.freezed.dart", "hash": "dcb73ec5e92f5cd66a306919f9e0dc21"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_2.dart", "hash": "dc92a928880163bbe0232a641f7f4276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/language.freezed.dart", "hash": "bf14400a81fc9d48be34b378dd1054cc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date.dart", "hash": "86b720af61fd71f6566c9e8d42412e85"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/messages.dart", "hash": "31e2179466decb4da4d2ae1e51938a51"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "a103dff72cbe4ef64a02c37dbfdc752d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclient2.dart", "hash": "c8f773e164112ed7174c2a8ac494c222"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/win32_registry.dart", "hash": "78f302720a7202dc38e0922492f5c7c3"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "hash": "7f37ea646d3e1b9f923f3af623128a0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/html_input_stream.dart", "hash": "ed02ce14880085c75d4dbc4b3145371d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclockadjustment.dart", "hash": "87db5c50521a6f935c7dd4196eb44001"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/playlists/playlist.dart", "hash": "593cc2e0c91e35bdf06cab3d2b321d54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iprovideclassinfo.dart", "hash": "874923f8d9b00546393faf2d37e102ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemconfigurerefresher.dart", "hash": "313def06f0e97cc30450b2ac2d1cdfdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_winrt_error_l1_1_0.g.dart", "hash": "892733d7fba692457dcd57fcd8e3dadd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/unpack_utf16.dart", "hash": "cfab296797450689ec04e7984e7d80e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/bitrate.dart", "hash": "d4cd01aae3de5182c34ef555d796d25f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_handle.freezed.dart", "hash": "b7f00aca92d0e4500aae314fb00eaac6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document.dart", "hash": "0f90625420cd7d017be4426f0bdaf0e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document_fragment.dart", "hash": "88acdeb4b5b5a9e5b057f7696935fc2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/with_parent.dart", "hash": "cff1275e3376c28419f9b54215ec8b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader3.dart", "hash": "e97932f0cef53e2c018203ac3cf1c7e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "d7c9baf97f1348c00c56f8d64a3ce53a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation4.dart", "hash": "5652ed8799ce8b441ea61e4baef419a4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensor.dart", "hash": "e78db72a9d4ba4e067c0afb9ab7c4518"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/and.dart", "hash": "1e9ed9cdf00b9449d9b72dcd00add4d3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "a732cf9cb336d70db5c1145f0e468953"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/optimize.dart", "hash": "a4acaadf6a817daad4c485f9c6741bca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "27a4ea7d50fcfd776a5d69fce0cd26ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrange2.dart", "hash": "6905ddd5343384c6898473c3d0a553a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/pages/channel_upload_page.dart", "hash": "c2b95a88df1d9298673d15ccc9ea85d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/js/jsparser/src/ast_visitor.dart", "hash": "34e3d6505549cc6ee2e87634545f627b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "ca3df05f249dbc5a38ebb86ee9a74a1e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement2.dart", "hash": "b84e7e0b89d2bad9ce9064ec3afc6119"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "5ac341d21fd38e1a3307100a5b3c3138"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "798f76b8076951e542aad4221a45d480"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/string.dart", "hash": "be2e3e8ab6ed0e2b2b554a26b78f91f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtableitempattern.dart", "hash": "983a75a70218eda5b2e68e97f3293f5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "ebef4cfdfb854b138f6bdbbf53e73f0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "6e320dd3d12f0e125541bc4b983dcfa7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "f1c0b135f35af022771e30409953e0f6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/ancestors.dart", "hash": "3f842dc9d82d8b21557bf598ff4ec83b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationspreadsheetitempattern.dart", "hash": "7c279a8dd6f509d8f7a9c5a29ea35852"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "26312d25d45c45d94edcfbaaec9217b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationspreadsheetpattern.dart", "hash": "fac91a50f448265e9a9f97994e8b529e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/entity_mapping.dart", "hash": "5abb58e10e8ea85ea5990a97ee20ae4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/common/thumbnail.freezed.dart", "hash": "70fca5027deca790df23a3127510db72"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_uploads_list.dart", "hash": "2bec9c3b5755753e44a38d4def71276a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/possessive.dart", "hash": "08e17247b131fb75466c336e9a11fcfe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "ba2f8adc4e6c096b09aac919580fffee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/letter.dart", "hash": "35ae3adcf5e51919e36509ef828107a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/idesktopwallpaper.dart", "hash": "80dcf36840d009af3563b72ef07995f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessioncontrol.dart", "hash": "ec79f631eff71e86c32b832c1724df5f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/method_channel_url_launcher.dart", "hash": "351ed98071b53d3c2e98d376f2a65a74"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationproxyfactoryentry.dart", "hash": "52312cfbaf36f7c5af3e9dd495a2d33a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "428549777327ddf7f2287b69cab7b68b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessionmanager2.dart", "hash": "abafcf39ea8af4f661e2141f3a4607e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/src/messages.g.dart", "hash": "814815839a4b6d2924a5a8661780b0cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/node_list.dart", "hash": "4068e834e069179f5df23c7868664c19"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/doctype.dart", "hash": "c2d76b78fb107e358b1ad967f15f1746"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeventsource.dart", "hash": "cd336df9936bd016c7eeadf2ef3b4ee3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationproxyfactory.dart", "hash": "5d461db74d04d7e270d13a5a8a340796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/logger.dart", "hash": "49b829330c9d1fa06c2856f5f2266921"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationcacherequest.dart", "hash": "4d161c7f1cf07d8222224f941e4241ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_6.dart", "hash": "7eaf5b7e19afccfa1bde4bf16bf53648"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/find.dart", "hash": "9f4ec4b8e28d5bf94cbd385aa48eb91f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/tokenizer.dart", "hash": "a8e51be045a7977648c023a4531317f2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextpattern2.dart", "hash": "1dfa85bd16bf08ae91f9cceb02ef1563"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/set_ansi.dart", "hash": "d30eba29d046c1a8b7f029838de6e49f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/device_info_plus.dart", "hash": "33970ebf975bcd8cde1fa7156460e38d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "10505aa641207501d9a0759bf2d6515e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "b8c09bf358fcebf2f4c9214d1007536d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellchecker2.dart", "hash": "28ceeb83a01abb867ca28f6c8b6e3e97"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_track.g.dart", "hash": "25ae23e7d3e9aeaee6136f867c1b5a8f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/exceptions/http_client_closed.dart", "hash": "cef31abaeae58133acb03d5b94e025c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iinspectable.dart", "hash": "c66a0a686960f09a12ed2eb99e4ad3c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/models/fragment.g.dart", "hash": "9f6faf19881cc052d7d5d6bab1c9cee3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channels.dart", "hash": "e11f763ed6bd06a946ccde164aa88d93"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/lazy.dart", "hash": "128e022b683572b60bce0c93cd05007c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "ec2260a55dbb3ff283297d9da97e130c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/LICENSE", "hash": "2a68e6b288e18606a93b3adf27dbf048"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/kernel32.g.dart", "hash": "f17de3d9e71117cf29f360cbfaab5348"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispvoice.dart", "hash": "9a998f6a132477da93895bae423e3ac1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/structs.dart", "hash": "b51cea8017e3cbb294fe3b8066265c7e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "d9a659644f1b667686f2c9b22545dc0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetworklistmanagerevents.dart", "hash": "ef4187427d8c318086d02bedaaee9300"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/lib/models/video_info.dart", "hash": "4409508f62a1d2c9637e318760f1967d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationwindowpattern.dart", "hash": "58739f2e107e62b5568a115d4bd0d523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_attributes.dart", "hash": "f1acbe1ea51585adf0a1ba560796bab1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "e85b30de1963bb6981d72b6027a66dd4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/analyzer.dart", "hash": "17aa54781ed25267f20b106de6b6d59a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/encoding_parser.dart", "hash": "109eeb63e43422d207e9ad771c2ab623"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumwbemclassobject.dart", "hash": "1480d297c2637befd67fbf59b4840a1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumstring.dart", "hash": "3f9dcf46067e3b9d3f41240d43c1c59c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/common/engagement.dart", "hash": "bb6912e6fc64b8bb9b1dfd5e66efa0e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/flatten.dart", "hash": "481d21ef07dee6f82302a015f989b597"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "bb020f793a10d8bb46c0bbc996bd0bfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "083722b0880e8e5981f9e33da11e449c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "166147b7bee5919995e69f8ca3e69d17"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/widgets.dart", "hash": "9f9b1fcdf4037b3b4c71ed65b57e87f8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/permission_handler_platform_interface.dart", "hash": "54e3fc58f0992b887be63771a3d82202"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/expression.dart", "hash": "79503c7448238b77502c169788e26dbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/reference.dart", "hash": "e7a9dcfeea903e16ba7ddc8cc31960d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "57b508bc908fd0950889e1d70ce36fdd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "0763a220fcb5274b6c228b8b440ddb2a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2906cf9308cbed8eb54ab1638dd5f56e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "9b76b249fb23172215a62d66bb393ed5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/service_status.dart", "hash": "5072fb1450640d8f46605ff67dafa147"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/any.dart", "hash": "35536afe2f48001aa7c588b131051b83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/username.dart", "hash": "12aca3a5f8477139aa7e17af75b26819"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "0119e0f7758ee8ef19baeae2b96cb389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/playlists/playlists.dart", "hash": "ca38ec1e94f21006e93fd62bd61939f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationexpandcollapsepattern.dart", "hash": "109f9e8f02a0c411aead05d93b5258f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_track_info.dart", "hash": "00f64eaaa434be08ad5eec3c1e57cc4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellcheckerchangedeventhandler.dart", "hash": "79ad9ffc538ede29c2cc688c386d717b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/framerate.freezed.dart", "hash": "01c16aa84d45e420ba6dab327f922f23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "893548eaf87a8fd903da6fa761ad5ec1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_handler_platform_interface.dart", "hash": "d3e01a9f299b192bb14b18062c49e562"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tree.dart", "hash": "01d34f3007e4fddbaf4794395c4d9276"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.0.0/lib/freezed_annotation.g.dart", "hash": "c978a3aa7fc156a9fd541a0bdffecc74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_value.dart", "hash": "002be4c072c0cc5c5e72b5ff6d0c490b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation2.dart", "hash": "db81a2e27f649abaf9de88c1f92b2686"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_value.dart", "hash": "6f3422c300e4f005e63a4246631f3372"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/common/common.dart", "hash": "fd897e3fc4cc8bb0244c349e951eac8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "eafb3b31ec7cebf556a529810d6f649a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_8.dart", "hash": "9dbdc6f759d68f1ba1b47b561de1e299"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemimagefactory.dart", "hash": "d04edc39b6d3477197606ec9c969e738"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/playlists/playlist_client.dart", "hash": "d4ed98cc50ca53d98730982933d9b06c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/video_resolution.dart", "hash": "fcbff602723da6515649b818d075e38a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "74902317f9caa3ba9c05b114d45d8a02"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "e3b1d07a31d08470207f2b668564a833"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_0.g.dart", "hash": "b9518d8d712e1322277d9f323e98281c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "1338341fe43eb21f20857cc392cf2f71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtexteditpattern.dart", "hash": "dec4d61e9b7fb95acd5269b1122ed1a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/audio_only_stream_info.dart", "hash": "3a2a6c67c8a74aac950a4626b1a3f567"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "1d893e6d648c41d8e3281a76a2320431"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationdockpattern.dart", "hash": "70dffa2d7939cf32863e11a6560de9b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/named_entities.dart", "hash": "c7e489fa5d00c1717fe499f3845c2abb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestpackagedependency.dart", "hash": "2a81dde5f9a7f961c0f86199e56b5f88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/combase.dart", "hash": "90ed8a12c97e362a162da690203df055"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/namespace.dart", "hash": "d7259aeee1602df30d051e8fc0523d91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechobjecttoken.dart", "hash": "28d0ea0e14e6f6d4fbc0784ea9e5b2ea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "3303320b233b1ca33a9e6e8c93e2d2c9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "b1884cfd8778cd71cea03ca8f4b39f4f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "2c777edec67bbb084e5608fb5f6b495b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellingerror.dart", "hash": "6338f981e88891ddf1b5674bc7baac0e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "15059e9824dd4a9e06136d8dfd91c26a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "4eb84c94445470d8bb6bb8e2666aa51a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_literal.dart", "hash": "8388d5e13155ebde873438c26dc4ca33"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement5.dart", "hash": "c5f70755693110e17c5b6bf2e04d7a54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.0.0/lib/freezed_annotation.dart", "hash": "2ee2dfbfe100e601618c8d9af2b51166"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechvoice.dart", "hash": "de4097d4b754d587b6c15dec5805d0e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iconnectionpoint.dart", "hash": "81f755ba6336a4b4cfc0e376b4f8244d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/video_client.dart", "hash": "521d3ebaf251b1dd032f918161417925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemclassobject.dart", "hash": "e1908e595346f6fa2b9cafaf3c4f4b15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrange3.dart", "hash": "4f4a2d291e23c96c7ae0d4dbc9598c54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/failure_joiner.dart", "hash": "322037160bbd0d8f16bd4e77abfc61f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatatables.dart", "hash": "9aaf9cf926a4121e7dad07c34de018d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE", "hash": "c458aafc65e8993663c76f96f54c51bc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/node.dart", "hash": "9ec244272cb6c8da46a6dd5f104f0dfe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/list_to_blob.dart", "hash": "56d7144236503f311a7d9a966eaf2fbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iknownfolder.dart", "hash": "6f95956e8269ea0bfb393ad11311b570"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/ole32.g.dart", "hash": "ae656f0ed8c596c679f657c63db1697e"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/lib/services/permission_service.dart", "hash": "79e0b8d3471838c760f887962007f9da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/result.dart", "hash": "bc503b6c5e3658a13efaee4e0638935a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifiledialogcustomize.dart", "hash": "3308bebb4908701a4b9fea36a346ff20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/lib/main.dart", "hash": "c1f97327e9fb93a6369831029900739c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "c2e0fa3415ed461288b6e2aecf569919"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifiledialog2.dart", "hash": "b44f7c15167b08017c2831fc2ea95c1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/extensions/helpers_extension.dart", "hash": "84d27d6c7cba5b2b89f6aa43c5d3b882"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement9.dart", "hash": "5c637f86e1850037099c738512c748c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/playlists/playlist_id.freezed.dart", "hash": "97c7acb385d5f9ce9d7827ebd1eebeb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/constants_nodoc.dart", "hash": "161940e07ff4c31a1235e5ff9b16108e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml.dart", "hash": "2c4c36a5cc838977cf822b6db5d9200a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadataassemblyimport.dart", "hash": "2c79ebc319fae192d118d1483a0a0b07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/prefix_name.dart", "hash": "fbb3e43ae57262b3fc190cb173a7b5bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_7.dart", "hash": "c3e5aaaf36524bf9927e80f60f3b0bdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/permute.dart", "hash": "8171c3b0d66f560aad82b73d43393092"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclock2.dart", "hash": "286726a4ae635c3cb149cd640c3c096f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/player/player_response.dart", "hash": "497fd27db88fe67b99e5bb0ff37ad672"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatadispenserex.dart", "hash": "9817557f3ca542a625629bbc0bb2c6b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/video.freezed.dart", "hash": "70c642069def4c4d3ab05c5499a09509"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/js/jsparser/src/noise.dart", "hash": "fafc40f490feab3f6226f136e5ea57d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/logging.dart", "hash": "60fd6d17602ae0c1d18e791d6b1b79cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/epsilon.dart", "hash": "b9283cabc57ae94b3c75f147903751fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemrefresher.dart", "hash": "5026f3bc8f63a10ffb208a35e304f40c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationgriditempattern.dart", "hash": "89cf0f2546a2003c136706514de87702"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "5b66a624b831dd9a7c94d59aaa10f8bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/common/thumbnail_set.freezed.dart", "hash": "91f9066b7256245ffb0b6592f2771b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/predicate.dart", "hash": "4fcb0c3d6a9c166d16c124c91e33dcb6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel.freezed.dart", "hash": "1cf8a165d1af6e609ef3dd69900be037"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/shared/annotations.dart", "hash": "9a469ff3de60c96cf2f9b0523b651782"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/lib/services/download_service.dart", "hash": "d08eec90efc6e9552359134f38a84e3e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtreewalker.dart", "hash": "6ff721f374b943a1562c8e05dbcf814f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imodalwindow.dart", "hash": "69cf74c98799489b81531323ecc6cc74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "0e708f7885d57fccc31cdb5020c2d9c7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tree_printer.dart", "hash": "0b59ef1fb417d687f41af0202ba86cfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/constants.dart", "hash": "a32a056475fc5b8abb7b5c6ca6c9d9b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessionmanager.dart", "hash": "53ef1e482a9021fe353d68c9f8a1affc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/pages/channel_about_page.dart", "hash": "a870689a8683c94b0e3e9adef4ef4c26"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "53cf0d76bfd70bfdc7e2edb4a18327f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isupporterrorinfo.dart", "hash": "16aa1ef738ce39b1d4a46e95bd8fdfd9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "2539eaeb4e2f2f69f678fd850c2332e8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "1ed34d373b037c1696e90bf7e4f249ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "b24af65afbe06cb00d5661df3d3083af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/end_element.dart", "hash": "813218451c1d8dd310e1233bd4ca7a4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/resolvable.dart", "hash": "f7329cc0811af555900320e49bd9686f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/parser.dart", "hash": "830859b7bec94f5f922eaba151827455"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_handle_l1_1_0.g.dart", "hash": "02f24ac14177b434c3bebf0a9a2980db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_decoder.dart", "hash": "d16fc08d820f892bacb508cc3e45935e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "34371da200382409d181bf9c3fcaefc7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumresources.dart", "hash": "01f72c7d1903117cf6423e4a5e5916eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/audio_track.g.dart", "hash": "26d834e014cc7e250ae91ffa8ac4f46a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/utils.dart", "hash": "8608f71f077e370ee14d37c711e6580e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/oleaut32.g.dart", "hash": "01776292c19efee31306cb0679ab3773"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "f9646c35238459f46dd9d87783813f08"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestapplicationsenumerator.dart", "hash": "c4029d239f6cfaf6d919757bbd59b403"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immnotificationclient.dart", "hash": "6ef9470e2055a4adeffe2c757ce4073f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/hls/hls_video_stream_info.dart", "hash": "7bc0ef5c7b5c5ee3e10ef893465db388"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/youtube_http_client.dart", "hash": "96acdb46dfe35fb724cc321316680098"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/visitor.dart", "hash": "9cc453290a0fea4e24b848a74967c59b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/delegate.dart", "hash": "5fc1a25f60cfa0a0280878377348c63c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/lowercase.dart", "hash": "044ac7a861e88a6b5e7e2d2c59ccb7bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationandcondition.dart", "hash": "cdddb39c933c3ad3633502639963a480"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "e6f282a4b33b70c7d1d06bec39b155f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispnotifysource.dart", "hash": "32170db149f7552573abb976a101f186"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "23f5fb6033bd02c94d263d1ed41fb90e"}, {"path": "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "ebc6759fa73c53bc12d581d9e1e4c821"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "db6f70d83d36597cc6bc3eaaffd10aaa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/hls_manifest.dart", "hash": "735ed8811d4c6bdff8a6f8ba17e4f6db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/LICENSE", "hash": "bc9fc033e43bc50355d359c5651952d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemobjectaccess.dart", "hash": "2eb057b74241d39f07417866b331168e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "e138cb83b907c09a4ac468dff69d43de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/parser.dart", "hash": "505fb0828e4fe58e1a49ddab272d7b71"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "2be9783170f41208ab65361d7cb0ddc4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/skip.dart", "hash": "be231020db4ff03ccedf0cab8d50d12d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/element.dart", "hash": "23db80d93d6f37b73648e830d1dda0f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifiledialog.dart", "hash": "3ba0400a5592af52e69a5f66816ecae2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/crypt32.g.dart", "hash": "032051419cbe678eeb63004a5ffbcfda"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/.dart_tool/package_config_subset", "hash": "18d8780d507a52b6777c0d3f2dc81190"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationannotationpattern.dart", "hash": "1c877d25a9c369ae60db386f8a1a35ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "95f488b1875988eb094e0ba71deb7deb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/text.dart", "hash": "f52860ffbd4c6858f092292d1589d556"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/constants.dart", "hash": "1ec635f2db97328558affe7a0c49fdeb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_1.g.dart", "hash": "92268162591ef8477b2f93a31ce9758c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "b3465d5b02dd4743d8d9f9e4170a1151"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/gdi32.g.dart", "hash": "dacaaea99d06403132897f545645cd7b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/filesize.freezed.dart", "hash": "52f95f5ee33ff3a0fdd59b28c2ac48a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement8.dart", "hash": "110d15e94f599373085aba42911eadcf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/unbounded.dart", "hash": "a617a91b12a3156406da1d95552aa4a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationscrollpattern.dart", "hash": "6f2c0852f8ccc6be4e3462b28b0e105f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader5.dart", "hash": "85574281bf7d7bee9722a21e092b4be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemhiperfenum.dart", "hash": "adebe1537e162fcbe4404ab29e94fef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/version.g.dart", "hash": "679b8cdea258a721b151994c5c4aa9b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/powrprof.g.dart", "hash": "b561eb6b0b72c619394c33c55b2e8301"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/event_codec.dart", "hash": "16d220671ba632751edb02e31809a2a1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "59cca02e92c0ff79aac0c54c50e3bd2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationcondition.dart", "hash": "0469c2fefb6084f264cd0df8bce7263a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/audio_track.freezed.dart", "hash": "35f27997bc5ca99b7d09363cc6e6aca5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "a108c1a02c56f9162ede59a7c30ed41d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/services.dart", "hash": "bab8606629135509c96d78f7253526ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumidlist.dart", "hash": "d404d0c85a223afe9402bc0c33f60886"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/set_string_array.dart", "hash": "dce5e400c1f0958583196f9db05de7b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/utils.dart", "hash": "7014dc257215cc17a58e5bf88855eff7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_handle.dart", "hash": "82df96dc90ae7d712629608437d408a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "31db92b0b980a193d02b613bb9c0f819"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/guid.dart", "hash": "1699b455a9ecda569a7b35310ea98027"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterator.dart", "hash": "4d16182e94ac3ec4a2804eb97efe7842"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "2627dee7fb363a5bb1cbc919699bcc84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensorcollection.dart", "hash": "c297d04053f831c4efb96b9e8dcafa0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches/matches_iterator.dart", "hash": "4c92351d347c52a00797317aa487600f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "hash": "78a284b2b08e4fd76528a88d9f9fe0be"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immdevice.dart", "hash": "19cbf88ec406061bbb2e9260b18510a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatadispenser.dart", "hash": "3fc24d3b43ff4a6b63811978cfb697e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml_events.dart", "hash": "71b9da53ac40a568e55525799518d891"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/video.dart", "hash": "73270543371ac4bc70331623d395dbbb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/format_exception.dart", "hash": "2128831f60d3870d6790e019887e77ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_link.dart", "hash": "2b8b3092e93f95978a0ab7d6e1527f60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/models/initial_data.dart", "hash": "7d982e6e2c564f0a647601db88c5f4e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "61af6ead2e2dc04677bcfb8c0c2104ab"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tree_base.dart", "hash": "61b8716847e9a3ca1bff526d7603b9a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_decoder.dart", "hash": "86727853a00a22df95e85607270896f7"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "hash": "693635b5258fe5f1cda720cf224f158c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/event_attribute.dart", "hash": "304fc982848b57cf13da0ec511f05ed9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "8e7a18cd739e24a264facecc38379085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material.dart", "hash": "ff1b06a4c51e36902ef2e5cf96495fea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/stream_container.g.dart", "hash": "b0468f13909741f1bd349d876694acc6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/material.dart", "hash": "f485bc1aa4fbdf87e17bfb8f80e39258"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/audio_track.dart", "hash": "c956fd03e238a6bed62dc06e563d6885"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "2cb8483d7aa2b998d4641e25a0425f67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/video_resolution.g.dart", "hash": "1fb6b1f326a6b6100a0183ed509ec71e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/user32.g.dart", "hash": "cfcb95b36d6698367184c63483b98f9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/youtube_api_client.dart", "hash": "d741af0f0c91672214f4da88dc3a6d6a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "hash": "220abb71a69244362e18af6167dac985"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "796af05466fbe319d5fc699b982ded0c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/win32.dart", "hash": "e3851989de381ed948b1c290a299a2c3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "a55ac84003178cdc783ca41a634500a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/flatten.dart", "hash": "b9f39f1eac6d7a0e9964cb4c7b2cd04a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_sysinfo_l1_2_3.g.dart", "hash": "4958f9fde0e398a31f8adfff89caf4d2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "4591f6273e6282466c0364d5331e50c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadataimport.dart", "hash": "d594c68b4bd289fec98c48d8753d4992"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "d3abf203392ec29c7ebbda6b41360d2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/cast.dart", "hash": "dc379ed249557649f50b9c27d0033be6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/sequence.dart", "hash": "0b1a431f52b54788ec3e9b6da7d87909"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/search/search_query.dart", "hash": "26693287bb6e7825ab0ad44f53725e2c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/builder.dart", "hash": "3131838b519fd1bcdc3486eb44d640d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/log_record.dart", "hash": "703c5e391948c58228960d4941618099"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/settable.dart", "hash": "f5f653af8a150de004f1b3ca1633bceb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/comparison.dart", "hash": "643ca26571c2ba94477233dbb914b1ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/core.dart", "hash": "b969cd0066fa07b8082edb76d2af77e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement4.dart", "hash": "90ad18cc7b92f34e912a56b5730afa4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/winrt_helpers.dart", "hash": "8a032ca2b66b8be21ce8368f80406db7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemfilter.dart", "hash": "ade1e21aa46fef03f979ccd8866f11e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/filesize.g.dart", "hash": "648294464412a540e8f733d23089de3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/_internal.dart", "hash": "ef4618b5bf737a7625f62d841127c69d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_part.g.dart", "hash": "e4c6222841a26574bbe8d2182cf3cd2d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "17a28a030318e2c8f8fd653e0b862d50"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_visitor.dart", "hash": "61e938fe770ed7331e39f1dda1b64dd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/js/jsparser/jsparser.dart", "hash": "0fbb920b7c894d5127985dec48434b93"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "bef4f4d150af7d7e46b13da4847f86fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/mixins/audio_stream_info.dart", "hash": "b6dd2431e4a0519317e05ddae55b689a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iclassfactory.dart", "hash": "adbacdd68acdd5e35ce91a3475a1be38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitem2.dart", "hash": "370b2d8885d48ae8d0316c6f0bb0ce05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/start_element.dart", "hash": "e11fc9210b4438654c11893b98ac66fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/null_mapping.dart", "hash": "4bc463f9c4b5496d8918b58070c10515"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "20d5458a880a0a10253cda660dbc42e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifileisinuse.dart", "hash": "704fe549f6257bb0025634b59ed121a0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/search/search_filter.dart", "hash": "834a566914eeb4f0236a8692936c423e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "13b920f66eba39405ab6c5487e5fc3f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/functions.dart", "hash": "e0123a0c77b03d27b3e2e899764c8d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement7.dart", "hash": "7b704b062a8ddbec0176222cf280563c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/subtree_selector.dart", "hash": "e5f1007517b62683935488c5189ebc5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/video_type.dart", "hash": "f00bd33f9cd5deda64ef3cb9478d6fd4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "203fbbac922589879ae44083b04a368b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "ced9d2439e23015bfc2bac438f598985"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/newline.dart", "hash": "bdd138e5e3c721f9272da59c10d7c5fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/streams.dart", "hash": "70bd7e7f2e548c1c90fce7273d67174a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/enum_helpers.dart", "hash": "32c8f2d4dc53cfe56f5fa637be2c52e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/language.g.dart", "hash": "a79b3e88f203f82787667cf34d042f69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/continuation.dart", "hash": "33717fbf3f4de35b5e494d284a252bb7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/list.dart", "hash": "ee730199a496cacbfd82312849e80523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_video.dart", "hash": "20955e601a089ad6aba2756b6ccdcfe5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "ca759e06438affc7dcbdd9c4d8f0dbb2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "f90fd4f8a9988f08157d132c23c8c08d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ierrorinfo.dart", "hash": "30b1813d0f71eea6cae262f843f196a5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "16903e1f0bc6b66d30a5804b7ae71fe5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "1fc85ca774e46295ca83c157718278e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/search/search.dart", "hash": "d0cd1ef8f1cda87f0fb827bcc081120c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement6.dart", "hash": "91ddaac3aa28c7527659e459c7c60e68"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/polyfill.dart", "hash": "bc0eb13caa9c0425831f18962dfe12ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/label.dart", "hash": "7de7aec8bf9b53488692403a3feb7672"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/constant.dart", "hash": "84391293163d781c7715a32ce43b3c7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationorcondition.dart", "hash": "4fade6330465dfa703dfe780444f8c7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/exceptions/youtube_explode_exception.dart", "hash": "8f0abb8c11e0dd9bfb4963f56c855c78"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/grammar.dart", "hash": "467b2b8993363b1b27f034f6c1cca476"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtransformpattern2.dart", "hash": "79a8e92d9a9d2929d6f432a49ce22c21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/dash_manifest.dart", "hash": "9c984ae68ea9f45482a1dfadbffbe203"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/matcher.dart", "hash": "faa18ee55924a5c65995875c94338d98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/hls/hls_muxed_stream_info.dart", "hash": "cc42822f0fa57cb2bbac37719665cdb1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "7b71540e417e6ea3f1134b4b677e0624"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/registry_key.dart", "hash": "b4b62a6be8e303f9d9a5b5408bf9106c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtogglepattern.dart", "hash": "1afa24c132e0ed2df8a83e7de36112e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/preprocessor_options.dart", "hash": "9f788a6e170d7968e9906e4d470e07f7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/winspool.g.dart", "hash": "21471f284b19e2a50238800becdb7419"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationcustomnavigationpattern.dart", "hash": "84de591d644b29f5e21052bd9c84263c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/enums.g.dart", "hash": "ebee8885b5afd397cfa8920eeccf88e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/eof.dart", "hash": "6a083480a6cb878f98927a9271454bd0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/device_info_plus_windows.dart", "hash": "9649ae3abaa2347c68833b467110ccf5"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/exceptions/request_limit_exceeded_exception.dart", "hash": "0602ce7f24ba580407a1d761ea653558"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/itypeinfo.dart", "hash": "5823785db5bc1f46ea00828058f2a973"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/utils.dart", "hash": "f5e920fcc3b932c9b189f55e8839ff02"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "0f6f972f6232b9d18cf00a9fa432127b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ivirtualdesktopmanager.dart", "hash": "ffd004f95154cc4fe026271fb8aed8cb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "e5163b554926dc261b556dc5d94245d2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.dart", "hash": "c6f78ebc1239a030ffc141df9b33aed1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "9e64d24aeed0ce5534422c6e4b454676"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationvirtualizeditempattern.dart", "hash": "34ac34257c6ee30da8c0b6de4d0a5444"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/comment.dart", "hash": "74fb000405fb96842a3ce15a519d8ae8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "82d1200fedba087f85961d6b1b9332fe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/pages/search_page.dart", "hash": "bcbb5a824277e101276fea0a2d4c886c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishelllink.dart", "hash": "49be686324cf0d48dcb5a31b280affbe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/url_launcher_platform_interface.dart", "hash": "9190f2442b5cf3eee32ab93156e97fb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/parser.dart", "hash": "e4d5eb474812b6fb78ddb16f9ddb9472"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/bthprops.g.dart", "hash": "cf53cbbbfbbbc6e5f3d0fab91b9f20a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationdragpattern.dart", "hash": "27ae743f7cf34ade61bc99eba4dedd81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "hash": "9633ac2bb6bd16fe5066b9905b6f0d1c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_format.dart", "hash": "52972df0cdc4805c2366bfc32b7a3403"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetworklistmanager.dart", "hash": "853dcca1f602ac69e366e5d6afcba80e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/propertykey.dart", "hash": "9e8d52d54397c70b7c20ef9436ca38c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationselectionpattern2.dart", "hash": "2066a7e06691045adc327c174ab0854f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/parser.dart", "hash": "13255a7d5a3edaa79e467810f1290f48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/clients/related_videos_client.dart", "hash": "9ca6e501a774aaf40d6d819cdfa32b37"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "e01f6851d87ad96cbdafcbfd282517e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/token.dart", "hash": "f81e0f51e6529eaf92d4e8d6196e4e13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_name.dart", "hash": "749e18efee29d6925d7c55e573d3eb2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/playlists/playlist_id.dart", "hash": "e2198815d09ec2048d229b4c1f5e32f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/stream_controller.dart", "hash": "36610a1406e3121619842b0f02be4322"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/exceptions/video_requires_purchase_exception.dart", "hash": "e3a924143700e028012fce883311beaf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/video_only_stream_info.dart", "hash": "16224060f6c2be0bc9e681b091e59540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_key.dart", "hash": "6a18f9347e6e639ebbbfb0a0ce98bb71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienummoniker.dart", "hash": "43758553461b83b7bf6696d099d81613"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immendpoint.dart", "hash": "1f7230510c62078bbebc01fa06f1fb8b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/stream_manifest.dart", "hash": "98d99d76f00d40ac50ac1c7536bc36f1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "e38cc213f0e4b4ed76471f4d70e20abe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/common/base_paged_list.dart", "hash": "bf0e2aebc1fcbb341cef1726293154ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "0c38ab3123facc4ec6f01ba31158c3ec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/following.dart", "hash": "662638321f1933cdab78277f222b8aa5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationnotcondition.dart", "hash": "1fec236f729d3217c13d42295fe3faf5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/stream_client.dart", "hash": "97b2e61d01794179f881e45d4ee3d99a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemresources.dart", "hash": "3546603acafc7773ff75c32041c86fce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name.dart", "hash": "da50c399c40281c66d3c2582ac225276"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page.dart", "hash": "6b16a4d19243ba00762af7e39dafc177"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/js/jsparser/src/charcode.dart", "hash": "45b0cb9e6cbb2865b222c2d0de19267d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/playlists/playlist.freezed.dart", "hash": "3ae7b53b18d406b59390701415a4d73e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/html_escape.dart", "hash": "efc823416c4e5e4dcced4cc2c3bbd89c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatatables2.dart", "hash": "f1f175eff474684786b1b6980f386aca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifileopendialog.dart", "hash": "79e42699e1c911b6f1ad9e3d54fdb3e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/pattern.dart", "hash": "d881c458d06573eb887bdf0f3ce9f586"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "dcb5ce635282f4390eca8dcb73737991"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/youtube_explode_base.dart", "hash": "f74e64608bcecd81318c200ab455b99a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app.dart", "hash": "66bb0d42812dbdcb77a351f5d79c74a4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitem.dart", "hash": "450065123492ed697e9b82cdd6742580"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation.dart", "hash": "a4282bea82459c80d5a209b34f6b6086"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "ff7c5f41b6493392c45ef30383f6af9b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "7b28ec35aed9cbc3319bf4c15d7b352a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/not.dart", "hash": "be4332e6d8c10f4a290e2a412399e1cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/netapi32.g.dart", "hash": "5f080ef031fd5b2c581f580bbdefd983"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/attribute_type.dart", "hash": "a9d570114e5a6e733fb029f6b3cffad7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/macros.dart", "hash": "8016baf49ccbce205455e3fc0bddbb17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_parent.dart", "hash": "a7ac3293430577fa9c028b0df6607fa4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader.dart", "hash": "54fa8ef3118882b34ee76276accb2c10"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/position.dart", "hash": "faedea5895c9ddd2b2c270817c61d1f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/character.dart", "hash": "f6f8ad33193db66deb89d68e406eeaf9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/character.dart", "hash": "c1d88c6f9a0dbed4be35c285ffac4da6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/declaration.dart", "hash": "7b254933211feaa1ea185b61dc9b12af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/models/stream_info_provider.dart", "hash": "cdbaa3d7945d0bccf4790207e4cf447f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "45beeaf92542183f39c458a87dcc81f7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/url_launcher_ios.dart", "hash": "11803ff481a58d66000cbea8c68e2af4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "8e043971337ae96a1e56aaf2256540ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parser_exception.dart", "hash": "a62996936bad6c27697a35bed070547d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "0a731b52181a917a08ac96b525f7d96b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/magnification.g.dart", "hash": "89a8b628ccf4c51b05b54a5cd9efccba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/models/fragment.dart", "hash": "7c625d69862ebae51384044bab3beff9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation5.dart", "hash": "d879c3156e19f2b290c4d6eed1de5e89"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "hash": "2efbb41d7877d10aac9d091f58ccd7b9"}, {"path": "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/bluetoothapis.g.dart", "hash": "dee1906acf6e5639024770351149df59"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxfile.dart", "hash": "bd68bf024d2e14c1e7fb775612927625"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/advapi32.g.dart", "hash": "ca80e89c29289e7bd0153b09a83813f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/type_exception.dart", "hash": "abf77351ef7991f21d4f50727b72d4ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationproxyfactorymapping.dart", "hash": "4cbac1eea383d9069adc6a969f8f66d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuri.dart", "hash": "3dd3dfc2d8c37cb47724659f8d2098e1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "c51e8dd4b4c6fc4a085adda93a75fdc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/cipher/cipher_operations.dart", "hash": "a5b0678924da41882c1aaa61683f3083"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/shared/types.dart", "hash": "7e327134a49991d7ba65bbfe46bb8f4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iinitializewithwindow.dart", "hash": "0748bf03bcf37edd1d571959e45a5cc0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellservice.dart", "hash": "bde779c0e81c5e48653c2dbc3d589cd3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/preceding.dart", "hash": "9d5375413b37f738384990ebdd6c6285"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "d9dd226ec96aec60f125c0f1f8d00344"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/definition.dart", "hash": "8680f57e6ae9665a5f051c06c1efc688"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/rometadata.g.dart", "hash": "cd81c79fc585d5ed8858c3c530ec90f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/src/messages.g.dart", "hash": "bee9a89328e73d06f9b915e157deffe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/exceptions/fatal_failure_exception.dart", "hash": "2148cba543e90a6b4906e368fcec5e7c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "3f7c50b425818ea563c8459cfd6f9d5a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtransformpattern.dart", "hash": "d2659325876491c5e79bf348a57ccb66"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "ec0bf24485bc5f9b825a382457f586e2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "1603f38e802a78686ee48e3554da22f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipropertystore.dart", "hash": "54900b8b687e3ae6fff475cdc4a79ef0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption.dart", "hash": "5a7c4bb38a3b0d6fb31602bd9ecac4a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/tag_exception.dart", "hash": "d851ccbe29621b2c3cf5556211b35b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/src/messages.g.dart", "hash": "07d545e5e568302b0453b8848be6a678"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_children.dart", "hash": "7c666bff17f2cfae821f93f0c5e66a64"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "2c525c85cb323db613ddc5eba4b902d4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "5638f5f2028c522b32626825f6bd5b7e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/utils.dart", "hash": "b3c645b67738d6348499b11034a6a801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextchildpattern.dart", "hash": "b74f3bf94bc012c39c228948b2434413"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/petitparser.dart", "hash": "6cb32004f228090f1200484076254c7a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "490fffadb78eb29c5fe209be7fe12370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/builder.dart", "hash": "d2b684e31e63b6c876b2c0266705447a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/muxed_stream_info.dart", "hash": "dea3512a9cdccf6468fda612b46c66af"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "be7392100d4028793c499a48ed55cf29"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/wlanapi.g.dart", "hash": "0d11940ff8ff1c4818bbf5978c55a04d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/predicate.dart", "hash": "ec001ba2712f91cadae858bfdfe622e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/wtsapi32.g.dart", "hash": "d165617d9abc7ef296c4fc875471fae7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/search/search_result.freezed.dart", "hash": "fdc420985b7de9725a7f761bc5c61dfc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "00dfe436d7f3546993ad86cc4f9ff655"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "036fc28dc98388abec4456e8142c530f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/simple_sparse_list-0.1.4/LICENSE", "hash": "fcf5efb1afa280102297c7f3d06c0f2a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "883b210f4cc20daebdb2834dbe4a512c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishelllinkdual.dart", "hash": "8a9ddc700c157e2eed7422823ba8f6f1"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "81893e0f4e920897a6bf9f22a362b2b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/src/messages.g.dart", "hash": "114597dbbcfb24754b14f8261211d90f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/hls/hls_audio_stream_info.dart", "hash": "c9369b8f47d8b89f0be8c5e0c467259f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/registry_hive.dart", "hash": "6ae62d29deefd524cb417afb7b9794bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishelllinkdatalist.dart", "hash": "4e8aaa21ed0ade8f7601849694c81ce5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/internal/reference.dart", "hash": "f25bbc73708cc35ac55836cbea772849"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "22cb97b7d09f329bab7ed148b4d181e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/videos.dart", "hash": "960eacb115460825cb22221b11880b93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/hls/hls_video_stream_info.g.dart", "hash": "7d22a0b07afb44055ea352aef13e1b01"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "0e0b94d805e193b69802ca99d5a51b27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/writer.dart", "hash": "21a6f7aab6021cd2c8c69f9cd78ae36d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationdroptargetpattern.dart", "hash": "a54cd317ab692f04c4fc26e8cd9cfce5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iunknown.dart", "hash": "71bc337251bdc832a7be27b05297de39"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/filetime.dart", "hash": "3c1a4805a8645f3bdc5369321eddb32c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "5af6304445e6664f6caca9ed4b5e885f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "05c5ca73bc4e912f53a324cfa508bbfe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/cache.dart", "hash": "e0cbefa359309715e5101bce98eb65e2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/lib/services/youtube_service.dart", "hash": "3f21201b0edb1454f3081cfd8ac63400"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/dom.dart", "hash": "6a64fecc9f1801803c9a6706f60ad958"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/visitor.dart", "hash": "27780bbb98adce3f00386fc6223bf2c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_track_info.freezed.dart", "hash": "ccad9d9b0568ccdce7a18763a3bc665b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/trimming.dart", "hash": "1db476563b55e241003667ca3669c0b8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/visitor.dart", "hash": "87e0c94a0dd945f819a8bd24a9ac5e67"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/types.dart", "hash": "4a1d1bdbd4e9be4c8af1a6c656730a66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_buffer.dart", "hash": "22acb270c1bb267ee16b3d64a3faa825"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permissions.dart", "hash": "1addb41b1ec88a6b5674bd3486e9e225"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_converter.dart", "hash": "2d069a48b5e0ffa386474977d2c91c90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemarray.dart", "hash": "1f105c42a107237821eb50094abf6b4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/irunningobjecttable.dart", "hash": "62c7a9476527a2724b820dc7a40a5105"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/src/messages.g.dart", "hash": "5b9ec782f9739612abc43813e94f2545"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/normalizer.dart", "hash": "b6fde0bb78218226247a2173dbf96ea5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/android_device_info.dart", "hash": "9bf109ecb182b597064ca9ae6f68b801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_4.dart", "hash": "54d72b1c5b9977ccdcb6cd95e8acc7e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/word.dart", "hash": "05e847132bc525d82c8f22363faaab59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/pages/player_config_base.dart", "hash": "4d0ce628d5ea11a12841ae53f1585839"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "8f24c8ed1935c6f08997d0b9acb5bf38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/unicode-1.1.8/LICENSE", "hash": "1972be0ad060bef702b5d8f866e3d23d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_track_info.g.dart", "hash": "dabbeb44dc8f8f9b6a8e121369d6a119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechobjecttokens.dart", "hash": "d2026a23132fde8bda02e43a141f020b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/search/search_list.dart", "hash": "58a46c6dd37d1cf118f15e433c984309"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/resolve.dart", "hash": "cbb8e1af9f1f0decfb6fc25a0725c51f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/url_launcher_macos.dart", "hash": "ff296a17d3582fcd8fe99bfb544a3978"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/token.dart", "hash": "a27310d4435c84885993bedb05adabfe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/limited.dart", "hash": "bfc3692929b6ffa40605428f3cc70e86"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/url_launcher_windows.dart", "hash": "792062b629f33f12bf4aa68dd6601c50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "hash": "c23f3b290b75c80a3b2be36e880f5f2d"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/lib/screens/video_details_screen.dart", "hash": "4490f56dec4c29590d7837d01cf8710b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/separated_list.dart", "hash": "031cc72717282ff9f7f588e73fb0fd0b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/js/js_engine.dart", "hash": "35f36e09f6c15b52c238c677bd3dcfbe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/js/jsparser/src/ast.dart", "hash": "7fdb5e7f991f5ee3a5fed3f276e04ee9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isequentialstream.dart", "hash": "2fd375e07e198a53eb645fb0e05de9d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationstylespattern.dart", "hash": "de4c7b647a0bbf39c8873977902389f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/bstr.dart", "hash": "dad362de854994a72a95c2ddfb4f4dd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/common/thumbnail.dart", "hash": "a0f2845d604d261d081fa7b9a553c63a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/pattern_iterable.dart", "hash": "f0ae0acd94eb48615e14f6c4d1f5b8e0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "9a977b88944bf59512e9d8aaeef93605"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/setupapi.g.dart", "hash": "875f7a3b998ac288c00e9f1edeb69342"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/exceptions/video_unplayable_exception.dart", "hash": "3e41c6d834f612b49026c4b2e327b1d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/comment.dart", "hash": "87546066dfc566126ed9357805535e97"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/css_class_set.dart", "hash": "fd47de61e362c730e345626317a8fc44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_about.freezed.dart", "hash": "240535bc3892ef79d73dcb7d111ca3c9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "160e007517eb9af8299b242a217c6ff9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/youtube_explode_dart.dart", "hash": "2d89cd5c075bf1c870c64dbb40fa0f21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/video_quality.dart", "hash": "68e898cdef9d9852efb3029da312f81b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iconnectionpointcontainer.dart", "hash": "d3c3db414185f054f187734f6e35734e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/video_id.g.dart", "hash": "76eaf686f5e26848701c8fcdaca0db42"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/related_videos_list.dart", "hash": "fe83800540cc88d23479b1ffa2501721"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/node_codec.dart", "hash": "1de9311ba0f47dfc96166daab936f705"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/idispatch.dart", "hash": "872b28cd09e937b0b3089f6c72cf310d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/inline.dart", "hash": "5b3e856525ee8046183fed46364f86e9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_track.dart", "hash": "fc6c89085346e4aaba6bf31930ec9b66"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/unicode-1.1.8/lib/unicode.dart", "hash": "9b1b7656590bc27c7ce026b51b005929"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationitemcontainerpattern.dart", "hash": "17cf81dd718b76ea3b1453b5f74e1cd9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationscrollitempattern.dart", "hash": "a3ab60b19b4725b3ea1d1b0cb1c64451"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib/model/base_device_info.dart", "hash": "4f0e33807b3ea8a3a5d03a85dbdf565f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/list_proxy.dart", "hash": "d1c07a46157914ec4aaa9aa9a957df37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/audio_only_stream_info.g.dart", "hash": "60e416868cd0590f4256abf97fc46bc7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "b33b1182e92dc3469db2563a33be2841"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "85e90b0b1f705d7db10d294017bcaf44"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/shell32.g.dart", "hash": "c313566bafbe45ee136f94f682b189fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/token.dart", "hash": "8006c8d72d7de5fbf9f6034104c30166"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "0434e70443094435172ff3d214d26bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/dxva2.g.dart", "hash": "4725ff0ec5208552bafe4d6459df82fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiostreamvolume.dart", "hash": "9c9f9bcb5f1fed59a9b35912bbd2cf56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_9.dart", "hash": "e7280a239aa04ba649fc950d8170cb3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/LICENSE", "hash": "aca2926dd73b3e20037d949c2c374da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/int_to_hexstring.dart", "hash": "73cb6deeb88fdcc320cf8e089d51531d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_part.dart", "hash": "01dd1d37f1dc891f3b4141324c091e72"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "cf5dc26d65244c12416f3411c6d79996"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrange.dart", "hash": "73aa79d0da8711d3fe796ddbfb5d7585"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/none_of.dart", "hash": "2080f99186cef2d2ec3f4c6c5b7c768b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "737642bf1a2d9ebd63c82016292b6b93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/code.dart", "hash": "2d312745691a82b398796ad2f38ac63c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/.dart_tool/flutter_build/405ee421726b55f17f6a608e2a427867/native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/any_of.dart", "hash": "8cd59827d2f99e2d6c62f2f38c275cf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_parent.dart", "hash": "7f47dda6ed10e33236d465680dc8c12b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/reflection/iterable.dart", "hash": "763f95cfee7dc8f35f6557eab7e94312"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/failure.dart", "hash": "2db6cf613a3f03e05a2c19ca6e14447b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/doctype.dart", "hash": "a0ff9321b483226cdbe4773e33779715"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/registry_value.dart", "hash": "3a76b28bc60cde234df7a417b1aa2ddb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifilesavedialog.dart", "hash": "dbf08eefdba5228054f4ebc6e6833c17"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/video_id.freezed.dart", "hash": "384de1abb5dc001b06f28a72fb801d96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterable.dart", "hash": "0ea87086ab38d0a0e292321e807293f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immdeviceenumerator.dart", "hash": "40e923ff78f2c9cba09cb0652fd34e26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestpackagedependenciesenumerator.dart", "hash": "0350d89163d2f0ced1ab36a631c63fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.0+1/lib/permission_handler.dart", "hash": "ae9b498a0c3fd784a628e57eb92307aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_shcore_scaling_l1_1_1.g.dart", "hash": "456c173e6b3ea24a522a57a8bcb3c0e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/registry_key_info.dart", "hash": "01167f5726fe27d877af4652c1e404a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/accept.dart", "hash": "740f17823564c3c7eca15bca5c110e17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/js/jsparser/src/parser.dart", "hash": "9a34973b5fdf856e7370d128f54b09e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/bitrate.freezed.dart", "hash": "34002f555e8bbbaa221cd9284e2a67d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/pointer_data.dart", "hash": "67f58e8d946fa7138e52e482d7c8f8d5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "1b3814e3cd3f2d9543c7ebaf88384e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "782760e5709624f38ebac3b7c728a792"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationrangevaluepattern.dart", "hash": "844c6caebd7d2ff68149b6881deae9f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/uppercase.dart", "hash": "7061b91c27425c907020fe54e569b9db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestospackagedependency.dart", "hash": "2880c8f7069a05a0e91104119bd918c4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationmultipleviewpattern.dart", "hash": "36e887f22fdae55fb0ea02599cd189f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "006c00513de6bd421565ec6ffd776337"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "3cb88cf9e4198e0d510b78aa005aa597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "9af22b49fd7407bc0ef05667f139defd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/context.dart", "hash": "a07f8e10a45176e8210b1bbac38f3e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/lookup.dart", "hash": "b76abc07781824bc4c06d11631188db0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "9796b800122953ccb2c3f40ba2120a94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/sibling.dart", "hash": "6cee72f673d593b0b84628bf243727a8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4895dd7c08da98c883cb21943f4ca4d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/dwmapi.g.dart", "hash": "07bef83d35c41f53d65fda935b414231"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationsynchronizedinputpattern.dart", "hash": "01077b1240404868a99cdfb2c892cdff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader6.dart", "hash": "33186ffed4f0249b40a7d6161b7c2351"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/iphlpapi.g.dart", "hash": "603e877f67a065e3f92f62c54940b121"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadataimport2.dart", "hash": "40ffb3e4d1023f052603c4ced82ef3a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_location.dart", "hash": "f91bd03132e9e671e87f0b9066647164"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/normalizer.dart", "hash": "5684bfb4916cd4ec19596b3fd6a7a93b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/models/youtube_page.dart", "hash": "0b542de2ca48a8cca4803e8b78dd3139"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/parser.dart", "hash": "7aac958977c79edf01e6ad44a726b52b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/stream_container.dart", "hash": "14c2859b60f72378cdd227f24e292fdb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/exceptions/video_unavailable_exception.dart", "hash": "56366b95c35dae7f506bbfb411d74cba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/predicate.dart", "hash": "9d95e55b0ed6080e677989c4e1e1cff6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/method_channel_permission_handler.dart", "hash": "219013e8bbe8cf19fde2d3520067ba39"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/registry_value_type.dart", "hash": "d2abc4bf73a6fd6720bf0bc3b1a461f2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "ed582bff49cac60fb08ccee9ccc7c573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/irestrictederrorinfo.dart", "hash": "5a217487ee7b63aad11dcecb6a1ef25b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "82ea4f7076bd7e32c383a2466518b943"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/device_info_plus_linux.dart", "hash": "c273c5105a2ca17896243db15f9b07ea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "b0aac7d00e469646d25550d1e4e77d12"}, {"path": "/Users/<USER>/development/flutter/bin/internal/engine.version", "hash": "4a414bd4083c489617af112624a0a365"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/search/search_result.dart", "hash": "4a401669548af2e40556e5c300a345ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextpattern.dart", "hash": "b208f3c23955741947ead4a75ca0aa3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/common/engagement.freezed.dart", "hash": "4cfac21091c7130b370efbe1db296230"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_comm_l1_1_1.g.dart", "hash": "33f4d3cc878470ce68dbb3087c0cf584"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/retry.dart", "hash": "c06b7a6076c2f8139ca748aeba975aa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.g.dart", "hash": "7ff35a1db7f2b80a156d464b075a09f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/heuristics.dart", "hash": "8c2d4b79b77cc7538fe0852335a90053"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessioncontrol2.dart", "hash": "63b3745a751f980bb7dabdd60ca65969"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_captions.dart", "hash": "38dd272ba4e429925007c8f66c46828d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_encoder.dart", "hash": "df916478c93bc4bc0bc9adb2cc286153"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/comments/comment.freezed.dart", "hash": "1ae277ef3a4ae759feebe874f6b18036"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationselectionitempattern.dart", "hash": "2132997caebcefd4e7f00446c795cede"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/allowed_keys_helpers.dart", "hash": "41696c18e708950dccaf7cb4f5b7b195"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/url_launcher_linux.dart", "hash": "9d67bda83980287cc1100fe7fad9e05d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/pattern_iterator.dart", "hash": "accb24637ddbe55d7a3f76e4618bdd22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumnetworks.dart", "hash": "dabc834de271cc395f1aa1b0e9484fc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationlegacyiaccessiblepattern.dart", "hash": "43e95fe0932b68e415f370a7b5500cfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/pages/watch_page.dart", "hash": "9a6b7917b07a9e0d1a4635a98164059d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader7.dart", "hash": "a60dd773b7d69b347521fb64257f9397"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechvoicestatus.dart", "hash": "0048b001d840547e4f9cd7be7fad486e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/search/search_client.dart", "hash": "ddf38f6fe819cc0f884536a302b5dd12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation3.dart", "hash": "befcd208b8f5a4c002c3f3fcaa09ccfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_apiquery_l2_1_0.g.dart", "hash": "43bf805d1c539a8158296b167f65064c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.0.0/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_writer.dart", "hash": "0537fb6d3d370f2fd868e2654361356a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/sequential.dart", "hash": "b5519514c9b9570c951c0da186030e29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/variant.dart", "hash": "ea1d5b4167d55eb79626b0ad1bfe6a58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_id.freezed.dart", "hash": "251ef3ea64166f95bf5141adb144d18d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationpropertycondition.dart", "hash": "55b96cf000403acfb7c7d32447a4e102"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_winrt_l1_1_0.g.dart", "hash": "5e93843d2c9e385658de36a256646d3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/language.dart", "hash": "c565d31f0031b14ed35e5105c444baa6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/js/jsparser/src/lexer.dart", "hash": "7190121d74b653d8ab80a35dff82e7a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/js/jsparser/src/annotations.dart", "hash": "14ca0f3b3eff76ddaff386679c549791"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/pick.dart", "hash": "c60b204fb5e7d501c0addb330c88d2de"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/property.dart", "hash": "9f56fbe65bf797a2eba907e0181e69ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/dtd/external_id.dart", "hash": "348e54c1032cec91d7a1a5cfce8c2098"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/string.dart", "hash": "1aaa0309ba77b0f57733e99543c455ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_client.dart", "hash": "27741a8e08dfcc4e8f7d66801c1e0e77"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader4.dart", "hash": "5a65f8839771af0fad5b2cf647703264"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/lib/screens/permission_screen.dart", "hash": "2f0530e83109b98ed58b632647a9da8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/scarddlg.g.dart", "hash": "f610f248b9a4132a9a920ccbf1d3cc5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel.dart", "hash": "4fab44761b25e9117f4fd52932f30b92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/video_controller.dart", "hash": "ffd4d9e10b2db7f015b26523084fbeab"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "7c4df8be3ef1b8c4564f6aa3c64ba65d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/parent.dart", "hash": "210257ed62edd783098ed34d7cfb0204"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immdevicecollection.dart", "hash": "7c246a3f46fba3b11765f3b103095947"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/comctl32.g.dart", "hash": "983eeb962574f2966f2fb4fb6f79af73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/token.dart", "hash": "595737cf044c5d483e4615a1b0e1db71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib/method_channel/method_channel_device_info.dart", "hash": "811012c24b5b0bca98b118bdbffbfccb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/comments/comment.dart", "hash": "52e64f10862942a27d067edd88480c6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/winmm.g.dart", "hash": "f81746c322f4a5269713665a1c9ab0fa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/video_id.dart", "hash": "4d8e2c12e4d3bde98e39dbc7f10360e3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "d953dedc9eee14dfb343f4c5988840c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.0+1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_comm_l1_1_2.g.dart", "hash": "27a9e4cafdd931f481840ea15b053cfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/uxtheme.g.dart", "hash": "c1b28c1b4b0d1008a417c0f6800696b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/exception.dart", "hash": "773da8c184ab316ec6998980a1448a1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/mutator.dart", "hash": "e105e8d3303975f4db202ed32d9aa4c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches.dart", "hash": "3459a8a962662899e6d1ed009af8ba45"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation6.dart", "hash": "2a3155e741ae2ae06a4a13cea8b719a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/exceptions/exceptions.dart", "hash": "a574b216f7e30d00cba75b0716cb47ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/filesize.dart", "hash": "9c7188b636c8da6e4f88ef3df8ad4bad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/callbacks.dart", "hash": "b020749262d0d602700cd21e6f41acdb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/dom_parsing.dart", "hash": "723a3d6fbd3de1ca1e39b70c5ddb5bcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement3.dart", "hash": "d1d288dcc528cf44d85a1788108201b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "8199cdd8c075bef2ed0811394702680d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/macos_device_info.dart", "hash": "2dad016b21ffd8671999ec7fee53d20c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/models.dart", "hash": "792752250b7a3d3bdb27d7616c40819a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "7b70bab5ac21ac24af3c971965617277"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/query_selector.dart", "hash": "072bc29df9af18240c9691c60edcc988"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/parser_pattern.dart", "hash": "79a5f25a1a9d4aa4689bf37171e1b615"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/set_string.dart", "hash": "097e09840cc00325fdbebaacd05f4827"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "3e1bb909dcd21ccd8bdc03ba57bf02b2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "db24bbb74875ecb216e8445bc10a0714"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/istream.dart", "hash": "f336bda23f89f07f340a3e16b0406f05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclient3.dart", "hash": "18b7345ba375180fbdb4bd38938f764d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "5aa32c5e6b696b66556b4f91bf5983a3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/descendants.dart", "hash": "ffaf08c52f141dda6e8be50b3e46ea50"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/linux_device_info.dart", "hash": "2989c1b0519c699587a5e31d87597fca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader2.dart", "hash": "9e2940d007af19bd5cf177e3be339363"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclock.dart", "hash": "889b2a51969dc04727672d045f214d9e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/lib/screens/home_screen.dart", "hash": "67279f2a92f0b6c3a13db94b8ef5879c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemlocator.dart", "hash": "8e0c72d73e1298cb4655ed8bfb6b9a37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/common/thumbnail_set.dart", "hash": "584661de6b6b2c9da855278b2f65acc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/map.dart", "hash": "822f0a79dfd6a3c997d2b898ec420b97"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/.dart_tool/flutter_build/405ee421726b55f17f6a608e2a427867/dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensormanager.dart", "hash": "d1ed08ede0f2e8ca979d575b3b6b8c64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/parser.dart", "hash": "668feba83ac51da82a0cd90d035b271b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclientduckingcontrol.dart", "hash": "8b1a62bc210bc087059381bbe1afa188"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "ffc66c213d3e015ff3e03298622c7141"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/dispatcher.dart", "hash": "cf07a18bf186a6805d8c384420518376"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "cb45dd3f32378f0acf6b8a514cdc6084"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "625b858bd9847eab75d2f3f6295a25bc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "0f61d8c0c0870ae724b64f2f2af816bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches/matches_iterable.dart", "hash": "037df9e7342fc8b812d985c8b6e8a0c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/not.dart", "hash": "d4acdced936c4825eed27ed61fc28660"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/video_only_stream_info.g.dart", "hash": "89ee488427535d124cfc027878875a59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/clients/closed_caption_client.dart", "hash": "b907630097bce2a4654f9dbe78996a00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/default_mapping.dart", "hash": "72bbe921b18b48d52eb45666e3c52729"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/windows_device_info.dart", "hash": "93faa63229d86e5b66c6fa4fd0554f1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "hash": "dd12f205ac5895a2bdf7d059cc4b83b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "3d7501e746aaa83cd9cc1b508d3f7ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellcheckerfactory.dart", "hash": "502de1243ccffce33e394a25f669b96b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechaudioformat.dart", "hash": "53ea2c5fd3a75125c1c5c2b4b323dd56"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/conversion_sink.dart", "hash": "efcbc6fd4212ea81281561abddbf29f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/link.dart", "hash": "c36f00a660d9aa87ebeab8672ccc6b32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/registry.dart", "hash": "a993ca2b8748f43da4eb160b12a15718"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersistfile.dart", "hash": "90f6ddbbfda2bb5dd2ab2e05c777def0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/comments/comments_list.dart", "hash": "bb14177db8e68c80164e099bcc6f6b2d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "737365e0b93f911e49f1ac1e5363564c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/group.dart", "hash": "f31a685ec42e95decf8c1937de3a5856"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/hls/hls_muxed_stream_info.g.dart", "hash": "2dd1408adc390b854ae18d18bb1eb6e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "6ea409faabc2d30760053a8936e45796"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/simple_sparse_list-0.1.4/lib/simple_sparse_list.dart", "hash": "b39a10359ba1c06e0e864a0cfb226599"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isimpleaudiovolume.dart", "hash": "fa2d84e94c7c5218924a785c41e40bb5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "4372cb3b63b820aff3fe67061bba3f9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_enum.dart", "hash": "4817a73df1c313cf6a6eb86774e7fc99"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "2fe7a1026669f97031a83f6da44d248b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/trie.dart", "hash": "f67497a47a5f8508d53dea861aa1e7ef"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "hash": "594a02a84320cea18e06458c446d98e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxpackagereader.dart", "hash": "59137da0b55aefe8a4074891792a55b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/exceptions.dart", "hash": "8bfe962047c432ca659d01097491e8b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationgridpattern.dart", "hash": "1183f96a464afb812132566262172088"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensordatareport.dart", "hash": "2fc9d0ed6032b1e7f7c310e4bff73f23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestproperties.dart", "hash": "25ff828118233f5852e97c3e15c2a5da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/video_sorting.dart", "hash": "9f8d27be8702e96f24e5e4c4e50faf64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/parser_match.dart", "hash": "d742d41268dec3da5e669142ae344928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/pages/channel_page.dart", "hash": "111a2e1d6a8a66f61f5e8d26a6ab6cc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "505d7dde41bffe17b69e52db6ab37d0c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersist.dart", "hash": "7e66e23bc10439da1ac0a1d84ef99ff3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "9d2e926705e7e23b2e34aa022cf55324"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/cdata.dart", "hash": "a1bc06d1d53e9b47b32fbdb4d323f44d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "88dbcce51623c5bb2cbe1e4a0f80a902"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellfolder.dart", "hash": "4a3cc462f40576025abe65fff32c0e63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption.g.dart", "hash": "d5edabe3df8e7041323bec69374aac48"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/comdlg32.g.dart", "hash": "5f5e77b562118490a9610f89d30b3990"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "b3686e0781f3148d75a64ebb2bfef609"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "12a21ff35182c138908274c8b66714d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/checked_helpers.dart", "hash": "c668a1bfe65f14c115a3294ac6502dca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/pattern.dart", "hash": "cd6d0f606129e54552c5fee950e32bfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/username.freezed.dart", "hash": "545822168c7a5ebe9e467c352213705b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/internal/undefined.dart", "hash": "bb00c98e50d3c71d4ab7ac7c46122f3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/result.dart", "hash": "fbca1545a7230f0ea39d7884a1722475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/enums.dart", "hash": "0126627ff99705319e77e37504e41d22"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelementarray.dart", "hash": "6c217e68a4edaca67f7c4cf9b53a2638"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_link.freezed.dart", "hash": "3196dfe8b381013f7c54965f34256e6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/structs.g.dart", "hash": "557ed823467a80fba26020510f836870"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/propsys.g.dart", "hash": "f21f9f81d251e1481096a9638691d07e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_video.freezed.dart", "hash": "6c8b30336d85272b94241dabc6db29ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_3.dart", "hash": "050f96bbbf01a1f86e208d7d8cc08901"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationboolcondition.dart", "hash": "276c1bd3d367490604e56ce50f55068b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrangearray.dart", "hash": "91c64b5db3c0685967d4a0ce319c5b59"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "fc0b4ef021be19542435a86743d8de7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/hls/hls_audio_stream_info.g.dart", "hash": "71d89123e0a6fd89481e86a04c3b74b2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/cast_list.dart", "hash": "87751ee02d315bd2d0c615bbf2803a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/annotator.dart", "hash": "2678ef31818710bda6610b84fc25d915"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/url_launcher_platform.dart", "hash": "0321281951240b7522f9b85dc24cb938"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/choice.dart", "hash": "253b43ba9075c77f9ce5267d91880da6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclient.dart", "hash": "70ef104bba3a3ffb69b8ffe7b1baf6e1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parent_exception.dart", "hash": "2ede71f09a240decbc57417850f8feb7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "f979a94d7bd35cf2a5168fbfb9bdcf1f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "955794ab8f9f2f33f660998c73ac222f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/optional.dart", "hash": "7d49b944ccc5ee228590126488731a95"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "0bb85eff209a2008dc5f47b2beda5bf3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/bitrate.g.dart", "hash": "4c81c99f374962186f827a7a6fb4d002"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "48b13baf494b39e894252da0a0f6e8c0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/dbghelp.g.dart", "hash": "740bd95d3579057bcb116d73ff96875d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/json_annotation.dart", "hash": "532a272d043c3dccd91b63d1b428dac9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iknownfoldermanager.dart", "hash": "76c591c08a5c91070981e5e18d21a645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/shlwapi.g.dart", "hash": "44636e7c7555564a463937cfb744b79a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemcontext.dart", "hash": "e060ba0218367e15110ec7001c8e07f2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/LICENSE", "hash": "1eadf5be3116dc31e5f04d4d6d352497"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "e892b3496135877dd5a0ea2ea2fc91e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tokenizer.dart", "hash": "80b8464423b79136f9fc5a427a1dafb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/repeating.dart", "hash": "282aa0046bbbfcbc30050f7fab282778"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwebauthenticationcoremanagerinterop.dart", "hash": "aef722a64f462b84d30dad6278040fb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imoniker.dart", "hash": "7be696b635be2ab16fad5ea6c723dd1e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/utils/codec.dart", "hash": "020552519d966b598cd3bb17849a3a49"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/comments/comments_client.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/mixins/stream_info.dart", "hash": "1f091ae55f115a917f8558935cc836f0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "2de077d432c4bb0a9525e9ab5d84913a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_manifest.dart", "hash": "fde73d07242d7042bc1fb4be4d0e128c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "726a60283ea6c3a38fbb1ea6139cb4f0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/winmd_constants.dart", "hash": "16115596ace5bc18b10c61743655c625"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/pubspec.yaml", "hash": "aa44d30bd9d54490133ae8cd73a85927"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/winscard.g.dart", "hash": "58538b3b7601a27a2d6c24c2d74161b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/types/muxed_stream_info.g.dart", "hash": "68e20d3fd36446de4801c43b81053bfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessionenumerator.dart", "hash": "379b3eed65ef945d342de0a84831c177"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_status.dart", "hash": "e644eae6cf851b3c46f83af266811a6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/list_converter.dart", "hash": "5f5f3a1074f40b8fc37c2b3ba5ec0432"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_about.dart", "hash": "92bcc479f826143e4df168e9fb31442d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "49194534260502aa020910c20fb3ad6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "f6816ebd27db772616d01f543b33d0f8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/event.dart", "hash": "1a7fe7a35dbd168a7f2e10065f4a3158"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/greedy.dart", "hash": "c138ee7ea69a6621403b3ba6973d4d7b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "39e18667c84e363d875147cc5dc6b2fa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "7f662c8207cea5db3d45f239a277ca9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtablepattern.dart", "hash": "acfd72d6ba5e1799364d54d9b5871d9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/attribute.dart", "hash": "3a8ae5977fc932c86b4b61e92db7a275"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumnetworkconnections.dart", "hash": "e34d7dd943080889020806fbd56e32d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_client.dart", "hash": "7d450bbc1822861490d11fbc0440c618"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/mixins/video_stream_info.dart", "hash": "8523965f2445b488bb66295daaa62102"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/exceptions/search_item_section_exception.dart", "hash": "15ee001a1c99505826ba8c284eac68ae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "44927d8a4e3825e7c3be0af91307d083"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/cipher/cipher_manifest.dart", "hash": "dad2e5c34559a7ef8379de09aa36878c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/web_browser_info.dart", "hash": "9e887cddbdf6c6c7c650a995832b127f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/exceptions/transient_failure_exception.dart", "hash": "fe90bbfc7161bd228f7beaa51dccf08d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "6f3424f2fc515abb888590b75c98e190"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_encoder.dart", "hash": "85ca5d0ad350ba37b698247c24cb70a0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/range.dart", "hash": "a6e57cd7b87262b784eb2efe6875a329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/digit.dart", "hash": "ea08cf8b71713e3535d5a2164a8bc7e1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "c86a43bc5abf7528416982490b4c0b8d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "2d4b5a2778f275040b5e438045607332"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "3ecea4d9c25299b0ea66c58256909437"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "38c6297c7e2085554452d28299d29a09"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/ios_device_info.dart", "hash": "2ac6c71ca1b3241eec36c619a2db2c4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/labeled.dart", "hash": "715bccb8e9ba9889573a60bf0e457402"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name_matcher.dart", "hash": "5c4dc37f36fc78823f785b92b944560d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "e27d4685e9e6aa906547a77095cc1ac5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/processing.dart", "hash": "5a7bd956aa537e95be882d4809232c39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib/device_info_plus_platform_interface.dart", "hash": "4b532e7a43e7a2a180a49c5869ec8ab4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/types.dart", "hash": "83bb9dfd0d336db35e2f8d73c2bdda85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetwork.dart", "hash": "d74c6cdab75d5d9b62ebf9cb07182a26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/nodes.dart", "hash": "8608080cdfc143d462b0f9947dc0d7c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_winrt_string_l1_1_0.g.dart", "hash": "acb5f13d5c2cb9a6c302487169ead3dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxfilesenumerator.dart", "hash": "9834ef8e867f05559620160927fc6964"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "fad2940dc1f4f3e4a0ebb5c7ff40a3a7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/framerate.g.dart", "hash": "74fe1af1b1eead7f1d84840b9f6e2b69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/node_type.dart", "hash": "57e5dc91c30bff1774eaaa45a798d0df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/each_event.dart", "hash": "91b72e3a75068042bd3b16de99d2c990"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ibindctx.dart", "hash": "48b80cd162c0a5db716c6df6af6d33a3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "3d27bed38f1893769396b5d23f94f15e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement.dart", "hash": "d1f3ee169c9fd7a9dec5eb03a4a33525"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/lib/widgets/download_option_card.dart", "hash": "dd33a7423671e2848b022dd3a1c49bd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/token.dart", "hash": "6c8afaf3db5be20a458530a92ff971d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tokenizer_base.dart", "hash": "e3bb2a25791065817d184fabfb8f7d0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationvaluepattern.dart", "hash": "3e61350adee7d79eed0c5d670bd6594e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumvariant.dart", "hash": "d0247fa5a3aacccf4023fe5c01469e14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersistmemory.dart", "hash": "4beadd212378edebcbcbd4f68f9a183a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/ntdll.g.dart", "hash": "b738d8d6cc15b84589aee0b3cabf344f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationinvokepattern.dart", "hash": "942a7879522bdf82258a3383893665a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "b9bfa2dc31960df2f1fd3aee88c3807e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersiststream.dart", "hash": "5f049e50ba97da4a4ead78fb7ff33dad"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "92137effa05660558f35cfc5845783bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/declaration.dart", "hash": "0ddfa36e71f58e8be68202ab6901bfdf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemservices.dart", "hash": "cad771ca5668c41d6aa03f3af838edd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/models/framerate.dart", "hash": "7932e8bd50ab714be2fee2cd1d401ed4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iagileobject.dart", "hash": "4bc403cec1c5846051bca88edb712a8c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iapplicationactivationmanager.dart", "hash": "c96999a0782dffe9bf8eeaf394caf3bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestpackageid.dart", "hash": "6216df76c2226292e1e6c05135e22217"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechbasestream.dart", "hash": "3cb4064cdabe9e96b0334d5877aafedf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/whitespace.dart", "hash": "f0df878443ef28db864b73e66f8206a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "98f06a29791e4f6ffc1ccefd18f323fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/pretty_writer.dart", "hash": "4c618cb90a20b93f23c554b8745d5f77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiorenderclient.dart", "hash": "d32d5d6e1a22dea95ed2e78d62d2aff9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechwaveformatex.dart", "hash": "95efdbdf332246f8a996a1219ed4a578"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/named.dart", "hash": "c5f3b8d4c2e6f53c5fcbdde1e0f03f4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/simple_name.dart", "hash": "208d1ef7a6cc2445551b3138139613bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellchecker.dart", "hash": "f9114e11eefdb88a452875b8d583e267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxfactory.dart", "hash": "93d835e43f33ca5ed96e6e85a392c1e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/where.dart", "hash": "bab2294ec70ff137aca684dd19203943"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/separated.dart", "hash": "641f0dfad31a545ac6fa5515e83920fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/cdata.dart", "hash": "008d33cc2aea11e7921ee238469947b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/access_rights.dart", "hash": "571943578e78006ea11a53ba5a541224"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/closed_captions/closed_caption_format.g.dart", "hash": "b0b5025213d83ad0eb2af70aacae974e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ichannelaudiovolume.dart", "hash": "1d94fcee85c9891374166b09889f8f09"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "6b48e1348ae677efad30c0a9d4600e38"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "72c318c3499a7a4d533965d32c6dface"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "577ec098e9f6651d7704fad48b4dd44a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/streams/mixins/hls_stream_info.dart", "hash": "029fe11141fe3ed7ffd0d3aa210e4a54"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestapplication.dart", "hash": "bc01545a1cca050f2067c0b6163a4755"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/text.dart", "hash": "d3de5e8090ec30687a667fdb5e01f923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumspellingerror.dart", "hash": "5f3836dd0624aa9d429b6a4d5b9d0321"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/xinput1_4.g.dart", "hash": "aa553bed1d888de4b53878cd701f022f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/pages/playlist_page.dart", "hash": "8a55b7b82d917aab572d828c1fc77dd3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "b3f8f8ba0560319908ddb5d9480a5788"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiocaptureclient.dart", "hash": "fcb48a4f74d20f9d0767c13fadb819df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/dialogs.dart", "hash": "3deaa1966e0bb94f98b6f402ad576915"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/css_printer.dart", "hash": "9a6fff298db26d4e059ebb664863ab18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/char.dart", "hash": "00456c7fcfc11e9ae46af126277652d5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "75fa80ab7762b14e35b11b93da96d4a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetworkconnection.dart", "hash": "cd2e632e852f1e82cc2084c49e16500a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/videos/comments/comments.dart", "hash": "f68a11ce66c485524d08071a1c61da66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/reverse_engineering/clients/comments_client.dart", "hash": "5a28b00132f4772ee1573a4d27dd9fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/separated_by.dart", "hash": "04b800051d2d913cafc3733ee1bb21c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "0201ee9c8aee2bb24db2c74b6c0cd485"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "f30e48d0892af0c99b54816673cff9ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/Documents/augment-projects/youtube_downloader/.dart_tool/flutter_build/405ee421726b55f17f6a608e2a427867/app.dill", "hash": "220abb71a69244362e18af6167dac985"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/lib/src/channels/channel_id.dart", "hash": "c04ce5cb92f732c9f3b71ce8f73afb13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_wsl_api_l1_1_0.g.dart", "hash": "cbe579638444243e204755d50bfeae50"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/level.dart", "hash": "49f3213e86d2bafdd814ac4df3d114ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/character_data_parser.dart", "hash": "a3044567a5c6d8b0e52367af1a23d5e1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/exception.dart", "hash": "5934d7c0ee1ff94ec21aad560e9ed8ba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/token_kind.dart", "hash": "4b721bbf0c0f68e346e09e254b6b8d5a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwinhttprequest.dart", "hash": "abcb8cf9c1f0bd0abc0fbf82868470fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/data.dart", "hash": "d7fab9eeba6ce2b3fae0a93d5622ac93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern.dart", "hash": "2108c716fd8198fa3a319a1ec6cadc9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/treebuilder.dart", "hash": "2c8ef2ed22dd79552a4d286b31817a27"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "0a546a51fffe9612c8c3cbebc609691c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "9bf11cc1ea784a251bf67350f02f910f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/url_launcher_android.dart", "hash": "42d0000dd58d923eb70183595232c299"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_5.dart", "hash": "a2f8f444e0f2d008fef980dd8df0bcac"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/constants_metadata.dart", "hash": "7a458ae2387a1d54657b7d330a7101e3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/processing.dart", "hash": "0ca8410c364e97f0bd676f3c7c3c9e32"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationselectionpattern.dart", "hash": "47221390638b312632445bde2ddc8b4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationobjectmodelpattern.dart", "hash": "3538e2e0fb5922d0a2639ae21ff1345f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}]}