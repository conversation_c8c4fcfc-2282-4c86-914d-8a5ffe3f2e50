import 'package:flutter/material.dart';
import '../models/video_info.dart';
import '../services/download_service.dart';
import '../widgets/download_option_card.dart';

class VideoDetailsScreen extends StatefulWidget {
  final VideoInfo videoInfo;

  const VideoDetailsScreen({
    super.key,
    required this.videoInfo,
  });

  @override
  State<VideoDetailsScreen> createState() => _VideoDetailsScreenState();
}

class _VideoDetailsScreenState extends State<VideoDetailsScreen> {
  final DownloadService _downloadService = DownloadService();
  bool _isDownloadingAudio = false;
  bool _isDownloadingVideo = false;
  double _downloadProgress = 0.0;
  String _downloadStatus = '';

  Future<void> _downloadAudio() async {
    setState(() {
      _isDownloadingAudio = true;
      _downloadProgress = 0.0;
      _downloadStatus = 'Preparing audio download...';
    });

    try {
      final filePath = await _downloadService.downloadAudio(
        videoId: widget.videoInfo.id,
        fileName: widget.videoInfo.title,
        onProgress: (progress) {
          setState(() {
            _downloadProgress = progress;
            _downloadStatus = 'Downloading audio... ${(progress * 100).toInt()}%';
          });
        },
      );

      _showSuccessDialog('Audio downloaded successfully!', filePath);
    } catch (e) {
      _showErrorDialog('Failed to download audio: ${e.toString()}');
    } finally {
      setState(() {
        _isDownloadingAudio = false;
        _downloadProgress = 0.0;
        _downloadStatus = '';
      });
    }
  }

  Future<void> _downloadVideo() async {
    // Show quality selection dialog
    final quality = await _showQualityDialog();
    if (quality == null) return;

    setState(() {
      _isDownloadingVideo = true;
      _downloadProgress = 0.0;
      _downloadStatus = 'Preparing video download...';
    });

    try {
      final filePath = await _downloadService.downloadVideo(
        videoId: widget.videoInfo.id,
        fileName: widget.videoInfo.title,
        quality: quality,
        onProgress: (progress) {
          setState(() {
            _downloadProgress = progress;
            _downloadStatus = 'Downloading video... ${(progress * 100).toInt()}%';
          });
        },
      );

      _showSuccessDialog('Video downloaded successfully!', filePath);
    } catch (e) {
      _showErrorDialog('Failed to download video: ${e.toString()}');
    } finally {
      setState(() {
        _isDownloadingVideo = false;
        _downloadProgress = 0.0;
        _downloadStatus = '';
      });
    }
  }

  Future<String?> _showQualityDialog() async {
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Video Quality'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('High Quality'),
              subtitle: const Text('Best available quality'),
              onTap: () => Navigator.pop(context, 'high'),
            ),
            ListTile(
              title: const Text('Low Quality'),
              subtitle: const Text('Smaller file size'),
              onTap: () => Navigator.pop(context, 'low'),
            ),
          ],
        ),
      ),
    );
  }

  void _showSuccessDialog(String message, String filePath) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Success'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: 8),
            Text(
              'Saved to: $filePath',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Video Details'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Video thumbnail and info
            Container(
              width: double.infinity,
              height: 200,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(widget.videoInfo.thumbnailUrl),
                  fit: BoxFit.cover,
                ),
              ),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.7),
                    ],
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.videoInfo.formattedDuration,
                        style: const TextStyle(
                          color: Colors.white,
                          backgroundColor: Colors.black54,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Video details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.videoInfo.title,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.videoInfo.author,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${widget.videoInfo.formattedViewCount} • ${widget.videoInfo.formattedDuration}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            // Download progress
            if (_downloadStatus.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(_downloadStatus),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(value: _downloadProgress),
                  ],
                ),
              ),

            // Download options
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Download Options',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),

            DownloadOptionCard(
              icon: Icons.audiotrack,
              title: 'Download Audio',
              subtitle: 'MP3 format, audio only',
              onTap: _downloadAudio,
              isLoading: _isDownloadingAudio,
            ),

            DownloadOptionCard(
              icon: Icons.videocam,
              title: 'Download Video',
              subtitle: 'MP4 format, video with audio',
              onTap: _downloadVideo,
              isLoading: _isDownloadingVideo,
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
