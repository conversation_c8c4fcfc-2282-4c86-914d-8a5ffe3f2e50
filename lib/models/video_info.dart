class VideoInfo {
  final String id;
  final String title;
  final String author;
  final Duration duration;
  final String thumbnailUrl;
  final String description;
  final int viewCount;

  VideoInfo({
    required this.id,
    required this.title,
    required this.author,
    required this.duration,
    required this.thumbnailUrl,
    required this.description,
    required this.viewCount,
  });

  String get formattedDuration {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    
    if (duration.inHours > 0) {
      return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
    } else {
      return "$twoDigitMinutes:$twoDigitSeconds";
    }
  }

  String get formattedViewCount {
    if (viewCount >= 1000000) {
      return "${(viewCount / 1000000).toStringAsFixed(1)}M views";
    } else if (viewCount >= 1000) {
      return "${(viewCount / 1000).toStringAsFixed(1)}K views";
    } else {
      return "$viewCount views";
    }
  }
}
