import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import 'youtube_service.dart';

class DownloadService {
  final YouTubeService _youtubeService = YouTubeService();

  /// Downloads audio as MP3 file
  Future<String> downloadAudio({
    required String videoId,
    required String fileName,
    required Function(double) onProgress,
  }) async {
    try {
      // Request storage permission
      await _requestStoragePermission();

      // Get audio streams with retry logic
      final audioStreams = await _youtubeService.getAudioStreams(videoId);
      if (audioStreams.isEmpty) {
        throw Exception('No audio streams available');
      }

      // Try different audio streams in order of preference
      final preferredStreams = _sortAudioStreamsByPreference(audioStreams);

      Exception? lastException;
      for (final audioStream in preferredStreams) {
        try {
          // Get download directory
          final directory = await _getDownloadDirectory();
          final filePath = '${directory.path}/${_sanitizeFileName(fileName)}.mp3';

          // Download the stream
          await _downloadStreamWithRetry(audioStream, filePath, onProgress);

          // Log the successful download location
          print('Audio downloaded successfully to: $filePath');
          return filePath;
        } catch (e) {
          lastException = Exception('Stream ${audioStream.tag} failed: ${e.toString()}');
          // Continue to next stream
        }
      }

      throw lastException ?? Exception('All audio streams failed');
    } catch (e) {
      throw Exception('Failed to download audio: ${e.toString()}');
    }
  }

  /// Downloads video file
  Future<String> downloadVideo({
    required String videoId,
    required String fileName,
    required String quality,
    required Function(double) onProgress,
  }) async {
    try {
      // Request storage permission
      await _requestStoragePermission();

      // First try muxed streams (video + audio combined)
      Exception? muxedStreamException;
      try {
        print('Attempting to get muxed streams for video: $videoId');
        final muxedStreams = await _youtubeService.getMuxedStreams(videoId);
        print('Found ${muxedStreams.length} muxed streams');

        if (muxedStreams.isNotEmpty) {
          // Try different video streams in order of preference
          final preferredStreams = _sortVideoStreamsByPreference(muxedStreams, quality);
          print('Sorted ${preferredStreams.length} muxed streams by preference');

          for (int i = 0; i < preferredStreams.length; i++) {
            final videoStream = preferredStreams[i];
            try {
              print('Trying muxed stream ${i + 1}/${preferredStreams.length}: ${videoStream.tag} (${videoStream.videoQuality.name}, ${videoStream.container.name})');

              // Get download directory
              final directory = await _getDownloadDirectory();
              final extension = videoStream.container.name;
              final filePath = '${directory.path}/${_sanitizeFileName(fileName)}.$extension';

              // Download the stream
              await _downloadStreamWithRetry(videoStream, filePath, onProgress);

              // Log the successful download location
              print('Video downloaded successfully to: $filePath');
              return filePath;
            } catch (e) {
              print('Muxed stream ${i + 1} failed: $e');
              // Continue to next stream
              continue;
            }
          }
          muxedStreamException = Exception('All ${preferredStreams.length} muxed streams failed');
        } else {
          muxedStreamException = Exception('No muxed streams available');
        }
      } catch (e) {
        muxedStreamException = Exception('Failed to get muxed streams: $e');
        print('Muxed streams failed: $e');
      }

      // Fallback: Try video-only streams (note: these won't have audio)
      Exception? videoOnlyException;
      try {
        print('Attempting to get video-only streams for video: $videoId');
        final videoStreams = await _youtubeService.getVideoStreams(videoId);
        print('Found ${videoStreams.length} video-only streams');

        if (videoStreams.isNotEmpty) {
          final preferredVideoStreams = _sortVideoOnlyStreamsByPreference(videoStreams, quality);
          print('Sorted ${preferredVideoStreams.length} video-only streams by preference');

          for (int i = 0; i < preferredVideoStreams.length; i++) {
            final videoStream = preferredVideoStreams[i];
            try {
              print('Trying video-only stream ${i + 1}/${preferredVideoStreams.length}: ${videoStream.tag} (${videoStream.videoQuality.name}, ${videoStream.container.name})');

              // Get download directory
              final directory = await _getDownloadDirectory();
              final extension = videoStream.container.name;
              final filePath = '${directory.path}/${_sanitizeFileName(fileName)}_video_only.$extension';

              // Download the stream
              await _downloadStreamWithRetry(videoStream, filePath, onProgress);

              // Log the successful download location
              print('Video (video-only) downloaded successfully to: $filePath');
              return filePath;
            } catch (e) {
              print('Video-only stream ${i + 1} failed: $e');
              // Continue to next stream
              continue;
            }
          }
          videoOnlyException = Exception('All ${preferredVideoStreams.length} video-only streams failed');
        } else {
          videoOnlyException = Exception('No video-only streams available');
        }
      } catch (e) {
        videoOnlyException = Exception('Failed to get video-only streams: $e');
        print('Video-only streams failed: $e');
      }

      // Combine error messages for better debugging
      final errorMessages = <String>[];
      if (muxedStreamException != null) {
        errorMessages.add('Muxed streams: ${muxedStreamException.toString()}');
      }
      if (videoOnlyException != null) {
        errorMessages.add('Video-only streams: ${videoOnlyException.toString()}');
      }

      final combinedError = errorMessages.isNotEmpty
          ? errorMessages.join('; ')
          : 'All video download methods failed';

      throw Exception('Video download failed - $combinedError. This video may be restricted or unavailable.');
    } catch (e) {
      throw Exception('Failed to download video: ${e.toString()}');
    }
  }

  /// Downloads a stream to file with retry logic and progress tracking
  Future<void> _downloadStreamWithRetry(
    StreamInfo streamInfo,
    String filePath,
    Function(double) onProgress,
  ) async {
    const maxRetries = 3;
    Exception? lastException;

    for (int attempt = 0; attempt < maxRetries; attempt++) {
      try {
        await _downloadStream(streamInfo, filePath, onProgress);
        return; // Success
      } catch (e) {
        lastException = Exception('Download attempt ${attempt + 1} failed: ${e.toString()}');

        // If this was the last attempt, throw the exception
        if (attempt == maxRetries - 1) {
          break;
        }

        // Wait before retrying (exponential backoff)
        await Future.delayed(Duration(seconds: (attempt + 1) * 2));

        // Clean up partial file if it exists
        final file = File(filePath);
        if (await file.exists()) {
          await file.delete();
        }
      }
    }

    throw lastException ?? Exception('Download failed after $maxRetries attempts');
  }

  /// Downloads a stream to file with progress tracking
  Future<void> _downloadStream(
    StreamInfo streamInfo,
    String filePath,
    Function(double) onProgress,
  ) async {
    final file = File(filePath);
    final sink = file.openWrite();

    try {
      final stream = _youtubeService.getStream(streamInfo);
      final totalBytes = streamInfo.size.totalBytes;
      int downloadedBytes = 0;

      await for (final chunk in stream) {
        sink.add(chunk);
        downloadedBytes += chunk.length;

        if (totalBytes > 0) {
          final progress = downloadedBytes / totalBytes;
          onProgress(progress);
        }
      }
    } finally {
      await sink.close();
    }
  }

  /// Sorts audio streams by preference (best quality first, then fallback options)
  List<AudioOnlyStreamInfo> _sortAudioStreamsByPreference(List<AudioOnlyStreamInfo> streams) {
    final sortedStreams = List<AudioOnlyStreamInfo>.from(streams);

    // Sort by preference: MP4 codec first, then by bitrate (highest first)
    sortedStreams.sort((a, b) {
      // Prefer MP4 codec
      final aIsMp4 = a.audioCodec.toLowerCase().contains('mp4') || a.audioCodec.toLowerCase().contains('aac');
      final bIsMp4 = b.audioCodec.toLowerCase().contains('mp4') || b.audioCodec.toLowerCase().contains('aac');

      if (aIsMp4 && !bIsMp4) return -1;
      if (!aIsMp4 && bIsMp4) return 1;

      // Then by bitrate (higher is better)
      return b.bitrate.bitsPerSecond.compareTo(a.bitrate.bitsPerSecond);
    });

    return sortedStreams;
  }

  /// Sorts video streams by preference based on quality setting
  List<MuxedStreamInfo> _sortVideoStreamsByPreference(List<MuxedStreamInfo> streams, String quality) {
    final sortedStreams = List<MuxedStreamInfo>.from(streams);

    if (quality == 'high') {
      // Sort by quality (highest first), then by container preference
      sortedStreams.sort((a, b) {
        // First by video quality
        final qualityComparison = b.videoQuality.index.compareTo(a.videoQuality.index);
        if (qualityComparison != 0) return qualityComparison;

        // Then prefer MP4 container
        final aIsMp4 = a.container.name.toLowerCase() == 'mp4';
        final bIsMp4 = b.container.name.toLowerCase() == 'mp4';

        if (aIsMp4 && !bIsMp4) return -1;
        if (!aIsMp4 && bIsMp4) return 1;

        return 0;
      });
    } else {
      // Sort by quality (lowest first), then by container preference
      sortedStreams.sort((a, b) {
        // First by video quality (lowest first for 'low' quality)
        final qualityComparison = a.videoQuality.index.compareTo(b.videoQuality.index);
        if (qualityComparison != 0) return qualityComparison;

        // Then prefer MP4 container
        final aIsMp4 = a.container.name.toLowerCase() == 'mp4';
        final bIsMp4 = b.container.name.toLowerCase() == 'mp4';

        if (aIsMp4 && !bIsMp4) return -1;
        if (!aIsMp4 && bIsMp4) return 1;

        return 0;
      });
    }

    return sortedStreams;
  }

  /// Sorts video-only streams by preference based on quality setting
  List<VideoOnlyStreamInfo> _sortVideoOnlyStreamsByPreference(List<VideoOnlyStreamInfo> streams, String quality) {
    final sortedStreams = List<VideoOnlyStreamInfo>.from(streams);

    if (quality == 'high') {
      // Sort by quality (highest first), then by container preference
      sortedStreams.sort((a, b) {
        // First by video quality
        final qualityComparison = b.videoQuality.index.compareTo(a.videoQuality.index);
        if (qualityComparison != 0) return qualityComparison;

        // Then prefer MP4 container
        final aIsMp4 = a.container.name.toLowerCase() == 'mp4';
        final bIsMp4 = b.container.name.toLowerCase() == 'mp4';

        if (aIsMp4 && !bIsMp4) return -1;
        if (!aIsMp4 && bIsMp4) return 1;

        return 0;
      });
    } else {
      // Sort by quality (lowest first), then by container preference
      sortedStreams.sort((a, b) {
        // First by video quality (lowest first for 'low' quality)
        final qualityComparison = a.videoQuality.index.compareTo(b.videoQuality.index);
        if (qualityComparison != 0) return qualityComparison;

        // Then prefer MP4 container
        final aIsMp4 = a.container.name.toLowerCase() == 'mp4';
        final bIsMp4 = b.container.name.toLowerCase() == 'mp4';

        if (aIsMp4 && !bIsMp4) return -1;
        if (!aIsMp4 && bIsMp4) return 1;

        return 0;
      });
    }

    return sortedStreams;
  }

  /// Gets the download directory
  Future<Directory> _getDownloadDirectory() async {
    if (Platform.isAndroid) {
      // Try multiple approaches to get the main Downloads folder

      // Method 1: Try to access /storage/emulated/0/Download directly
      try {
        final mainDownloadsDir = Directory('/storage/emulated/0/Download');
        if (await mainDownloadsDir.exists()) {
          // Test if we can write to it
          try {
            final testFile = File('${mainDownloadsDir.path}/.test_write_${DateTime.now().millisecondsSinceEpoch}');
            await testFile.writeAsString('test');
            await testFile.delete();
            print('Using main Downloads folder: ${mainDownloadsDir.path}');
            return mainDownloadsDir;
          } catch (e) {
            print('Cannot write to main Downloads folder: $e');
          }
        }
      } catch (e) {
        print('Main Downloads folder not accessible: $e');
      }

      // Method 2: Try using getDownloadsDirectory() if available
      try {
        final downloadsDir = await getDownloadsDirectory();
        if (downloadsDir != null && await downloadsDir.exists()) {
          print('Using getDownloadsDirectory: ${downloadsDir.path}');
          return downloadsDir;
        }
      } catch (e) {
        print('getDownloadsDirectory failed: $e');
      }

      // Method 3: Try to construct path from external storage
      try {
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          // Parse the path to get to public storage
          // From: /storage/emulated/0/Android/data/package/files
          // To: /storage/emulated/0/Download
          final pathParts = externalDir.path.split('/');
          if (pathParts.length >= 4) {
            final publicDownloadsPath = '/${pathParts[1]}/${pathParts[2]}/${pathParts[3]}/Download';
            final publicDownloadsDir = Directory(publicDownloadsPath);

            if (await publicDownloadsDir.exists()) {
              try {
                final testFile = File('${publicDownloadsDir.path}/.test_write_${DateTime.now().millisecondsSinceEpoch}');
                await testFile.writeAsString('test');
                await testFile.delete();
                print('Using constructed Downloads path: ${publicDownloadsDir.path}');
                return publicDownloadsDir;
              } catch (e) {
                print('Cannot write to constructed Downloads path: $e');
              }
            } else {
              // Try to create the Downloads directory
              try {
                await publicDownloadsDir.create(recursive: true);
                print('Created and using Downloads directory: ${publicDownloadsDir.path}');
                return publicDownloadsDir;
              } catch (e) {
                print('Cannot create Downloads directory: $e');
              }
            }
          }
        }
      } catch (e) {
        print('External storage method failed: $e');
      }

      // Method 4: Fallback to external storage with Downloads subfolder
      try {
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          final downloadDir = Directory('${externalDir.path}/Downloads');
          if (!await downloadDir.exists()) {
            await downloadDir.create(recursive: true);
          }
          print('Using external storage Downloads subfolder: ${downloadDir.path}');
          return downloadDir;
        }
      } catch (e) {
        print('External storage Downloads subfolder failed: $e');
      }
    }

    // Final fallback for iOS or if all Android methods fail
    final appDir = await getApplicationDocumentsDirectory();
    final downloadDir = Directory('${appDir.path}/Downloads');
    if (!await downloadDir.exists()) {
      await downloadDir.create(recursive: true);
    }
    print('Using app documents folder: ${downloadDir.path}');
    return downloadDir;
  }

  /// Gets a user-friendly description of where the file was saved
  String getDownloadLocationDescription(String filePath) {
    if (Platform.isAndroid) {
      if (filePath.contains('/storage/emulated/0/Download/')) {
        return 'Saved to device Downloads folder';
      } else if (filePath.contains('/Android/data/') && filePath.contains('/Downloads/')) {
        return 'Saved to app Downloads folder';
      } else if (filePath.contains('/Android/data/')) {
        return 'Saved to app storage folder';
      } else {
        return 'Saved to external storage';
      }
    } else {
      return 'Saved to app documents folder';
    }
  }

  /// Requests storage permission
  Future<void> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      // For Android 13+ (API 33+), we need different permissions
      // For older versions, we need WRITE_EXTERNAL_STORAGE

      // Check Android version and request appropriate permissions
      try {
        // For Android 13+ (API 33+), request media permissions
        if (await _isAndroid13OrHigher()) {
          // Request media permissions for Android 13+
          final status = await [
            Permission.videos,
            Permission.audio,
          ].request();

          if (status[Permission.videos] != PermissionStatus.granted ||
              status[Permission.audio] != PermissionStatus.granted) {
            throw Exception('Media permissions are required to save files to Downloads folder');
          }
        } else {
          // For older Android versions, request storage permission
          final status = await Permission.storage.request();
          if (status != PermissionStatus.granted) {
            throw Exception('Storage permission is required to save files to Downloads folder');
          }
        }
      } catch (e) {
        throw Exception('Failed to request permissions: ${e.toString()}');
      }
    }
  }

  /// Checks if the device is running Android 13 or higher
  Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;

    // This is a simplified check - in a real app you might want to use
    // device_info_plus package for more accurate version detection
    try {
      // Check if we can access the new media permissions
      // If Permission.videos is available, we're likely on Android 13+
      await Permission.videos.status;
      return true;
    } catch (e) {
      return false; // If Permission.videos doesn't exist, we're on older Android
    }
  }

  /// Sanitizes filename for safe file system usage
  String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }
}
