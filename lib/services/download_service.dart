import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import 'youtube_service.dart';

class DownloadService {
  final YouTubeService _youtubeService = YouTubeService();

  /// Downloads audio as MP3 file
  Future<String> downloadAudio({
    required String videoId,
    required String fileName,
    required Function(double) onProgress,
  }) async {
    try {
      // Request storage permission
      await _requestStoragePermission();

      // Get audio streams
      final audioStreams = await _youtubeService.getAudioStreams(videoId);
      if (audioStreams.isEmpty) {
        throw Exception('No audio streams available');
      }

      // Select the best quality audio stream
      final audioStream = audioStreams
          .where((stream) => stream.audioCodec.contains('mp4'))
          .reduce((a, b) => a.bitrate.bitsPerSecond > b.bitrate.bitsPerSecond ? a : b);

      // Get download directory
      final directory = await _getDownloadDirectory();
      final filePath = '${directory.path}/${_sanitizeFileName(fileName)}.mp3';

      // Download the stream
      await _downloadStream(audioStream, filePath, onProgress);

      return filePath;
    } catch (e) {
      throw Exception('Failed to download audio: ${e.toString()}');
    }
  }

  /// Downloads video file
  Future<String> downloadVideo({
    required String videoId,
    required String fileName,
    required String quality,
    required Function(double) onProgress,
  }) async {
    try {
      // Request storage permission
      await _requestStoragePermission();

      // Get muxed streams (video + audio)
      final muxedStreams = await _youtubeService.getMuxedStreams(videoId);
      if (muxedStreams.isEmpty) {
        throw Exception('No video streams available');
      }

      // Select stream based on quality preference
      MuxedStreamInfo selectedStream;
      if (quality == 'high') {
        // Get the highest quality stream
        selectedStream = muxedStreams.reduce((a, b) =>
            a.videoQuality.index > b.videoQuality.index ? a : b);
      } else {
        // Get the lowest quality stream
        selectedStream = muxedStreams.reduce((a, b) =>
            a.videoQuality.index < b.videoQuality.index ? a : b);
      }

      // Get download directory
      final directory = await _getDownloadDirectory();
      final extension = selectedStream.container.name;
      final filePath = '${directory.path}/${_sanitizeFileName(fileName)}.$extension';

      // Download the stream
      await _downloadStream(selectedStream, filePath, onProgress);

      return filePath;
    } catch (e) {
      throw Exception('Failed to download video: ${e.toString()}');
    }
  }

  /// Downloads a stream to file with progress tracking
  Future<void> _downloadStream(
    StreamInfo streamInfo,
    String filePath,
    Function(double) onProgress,
  ) async {
    final file = File(filePath);
    final sink = file.openWrite();

    try {
      final stream = _youtubeService.getStream(streamInfo);
      final totalBytes = streamInfo.size.totalBytes;
      int downloadedBytes = 0;

      await for (final chunk in stream) {
        sink.add(chunk);
        downloadedBytes += chunk.length;

        if (totalBytes > 0) {
          final progress = downloadedBytes / totalBytes;
          onProgress(progress);
        }
      }
    } finally {
      await sink.close();
    }
  }

  /// Requests storage permission
  Future<void> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      if (!status.isGranted) {
        throw Exception('Storage permission denied');
      }
    }
  }

  /// Gets the download directory
  Future<Directory> _getDownloadDirectory() async {
    if (Platform.isAndroid) {
      final directory = await getExternalStorageDirectory();
      final downloadDir = Directory('${directory!.path}/Downloads');
      if (!await downloadDir.exists()) {
        await downloadDir.create(recursive: true);
      }
      return downloadDir;
    } else {
      return await getApplicationDocumentsDirectory();
    }
  }

  /// Sanitizes filename for safe file system usage
  String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }
}
