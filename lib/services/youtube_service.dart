import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import '../models/video_info.dart';

class YouTubeService {
  late final YoutubeExplode _youtubeExplode;

  YouTubeService() {
    _youtubeExplode = YoutubeExplode();
  }

  /// Validates if the given URL is a valid YouTube URL
  bool isValidYouTubeUrl(String url) {
    try {
      VideoId.parseVideoId(url);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Extracts video information from YouTube URL
  Future<VideoInfo> getVideoInfo(String url) async {
    try {
      final videoId = VideoId(url);
      final video = await _youtubeExplode.videos.get(videoId);

      return VideoInfo(
        id: video.id.value,
        title: video.title,
        author: video.author,
        duration: video.duration ?? Duration.zero,
        thumbnailUrl: video.thumbnails.highResUrl,
        description: video.description,
        viewCount: video.engagement.viewCount,
      );
    } catch (e) {
      throw Exception('Failed to get video info: ${e.toString()}');
    }
  }

  /// Gets available audio streams for download with retry logic
  Future<List<AudioOnlyStreamInfo>> getAudioStreams(String videoId) async {
    try {
      final manifest = await _getManifestWithRetry(videoId);
      final audioStreams = manifest.audioOnly.toList();

      if (audioStreams.isEmpty) {
        throw Exception('No audio streams available for this video');
      }

      return audioStreams;
    } catch (e) {
      throw Exception('Failed to get audio streams: ${e.toString()}');
    }
  }

  /// Gets available video streams for download with retry logic
  Future<List<VideoOnlyStreamInfo>> getVideoStreams(String videoId) async {
    try {
      final manifest = await _getManifestWithRetry(videoId);
      final videoStreams = manifest.videoOnly.toList();

      if (videoStreams.isEmpty) {
        throw Exception('No video streams available for this video');
      }

      return videoStreams;
    } catch (e) {
      throw Exception('Failed to get video streams: ${e.toString()}');
    }
  }

  /// Gets muxed streams (video + audio combined) with retry logic
  Future<List<MuxedStreamInfo>> getMuxedStreams(String videoId) async {
    try {
      final manifest = await _getManifestWithRetry(videoId);
      final muxedStreams = manifest.muxed.toList();

      if (muxedStreams.isEmpty) {
        throw Exception('No muxed streams available for this video');
      }

      return muxedStreams;
    } catch (e) {
      throw Exception('Failed to get muxed streams: ${e.toString()}');
    }
  }

  /// Gets manifest with retry logic and different approaches
  Future<StreamManifest> _getManifestWithRetry(String videoId) async {
    Exception? lastException;

    // Try multiple times with different delays
    for (int attempt = 0; attempt < 3; attempt++) {
      try {
        // Add progressive delay between attempts
        if (attempt > 0) {
          await Future.delayed(Duration(seconds: attempt * 2));
        }

        final manifest = await _youtubeExplode.videos.streamsClient.getManifest(videoId);

        // Validate that we got some streams
        if (manifest.muxed.isEmpty && manifest.audioOnly.isEmpty && manifest.videoOnly.isEmpty) {
          throw Exception('No streams available in manifest');
        }

        return manifest;
      } catch (e) {
        final errorMessage = e.toString().toLowerCase();

        // Check for specific error types
        if (errorMessage.contains('403') || errorMessage.contains('forbidden')) {
          lastException = Exception('Video access forbidden (403). This video may be restricted, private, or region-blocked.');
        } else if (errorMessage.contains('404') || errorMessage.contains('not found')) {
          lastException = Exception('Video not found (404). The video may have been deleted or made private.');
        } else if (errorMessage.contains('unavailable')) {
          lastException = Exception('Video unavailable. This video may be restricted in your region or deleted.');
        } else {
          lastException = Exception('Attempt ${attempt + 1} failed: ${e.toString()}');
        }

        // If this is the last attempt, break
        if (attempt == 2) {
          break;
        }
      }
    }

    throw lastException ?? Exception('Failed to get video manifest after multiple attempts');
  }

  /// Gets the stream for downloading
  Stream<List<int>> getStream(StreamInfo streamInfo) {
    return _youtubeExplode.videos.streamsClient.get(streamInfo);
  }

  void dispose() {
    _youtubeExplode.close();
  }
}
