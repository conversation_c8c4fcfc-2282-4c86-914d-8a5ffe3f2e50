import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import '../models/video_info.dart';

class YouTubeService {
  final YoutubeExplode _youtubeExplode = YoutubeExplode();

  /// Validates if the given URL is a valid YouTube URL
  bool isValidYouTubeUrl(String url) {
    try {
      VideoId.parseVideoId(url);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Extracts video information from YouTube URL
  Future<VideoInfo> getVideoInfo(String url) async {
    try {
      final videoId = VideoId(url);
      final video = await _youtubeExplode.videos.get(videoId);
      
      return VideoInfo(
        id: video.id.value,
        title: video.title,
        author: video.author,
        duration: video.duration ?? Duration.zero,
        thumbnailUrl: video.thumbnails.highResUrl,
        description: video.description,
        viewCount: video.engagement.viewCount,
      );
    } catch (e) {
      throw Exception('Failed to get video info: ${e.toString()}');
    }
  }

  /// Gets available audio streams for download
  Future<List<AudioOnlyStreamInfo>> getAudioStreams(String videoId) async {
    try {
      final manifest = await _youtubeExplode.videos.streamsClient.getManifest(videoId);
      return manifest.audioOnly.toList();
    } catch (e) {
      throw Exception('Failed to get audio streams: ${e.toString()}');
    }
  }

  /// Gets available video streams for download
  Future<List<VideoOnlyStreamInfo>> getVideoStreams(String videoId) async {
    try {
      final manifest = await _youtubeExplode.videos.streamsClient.getManifest(videoId);
      return manifest.videoOnly.toList();
    } catch (e) {
      throw Exception('Failed to get video streams: ${e.toString()}');
    }
  }

  /// Gets muxed streams (video + audio combined)
  Future<List<MuxedStreamInfo>> getMuxedStreams(String videoId) async {
    try {
      final manifest = await _youtubeExplode.videos.streamsClient.getManifest(videoId);
      return manifest.muxed.toList();
    } catch (e) {
      throw Exception('Failed to get muxed streams: ${e.toString()}');
    }
  }

  /// Gets the stream for downloading
  Stream<List<int>> getStream(StreamInfo streamInfo) {
    return _youtubeExplode.videos.streamsClient.get(streamInfo);
  }

  void dispose() {
    _youtubeExplode.close();
  }
}
