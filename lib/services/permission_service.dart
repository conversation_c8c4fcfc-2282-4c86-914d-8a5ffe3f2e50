import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  bool _permissionsRequested = false;
  bool _permissionsGranted = false;

  bool get permissionsGranted => _permissionsGranted;

  void resetPermissionState() {
    _permissionsRequested = false;
    _permissionsGranted = false;
  }

  Future<bool> requestPermissionsOnStartup() async {
    if (_permissionsRequested) return _permissionsGranted;
    _permissionsRequested = true;

    if (!Platform.isAndroid) {
      _permissionsGranted = true;
      return true;
    }

    try {
      final sdkInt = await _getAndroidSdkInt();
      print('Android SDK version: $sdkInt');

      if (sdkInt >= 33) {
        // Android 13+ (API 33+): granular media access
        print('Requesting media permissions for Android 13+');
        final status = await [
          Permission.videos,
          Permission.audio,
        ].request();

        _permissionsGranted =
            status[Permission.videos] == PermissionStatus.granted &&
            status[Permission.audio] == PermissionStatus.granted;

      } else if (sdkInt >= 30) {
        // Android 11–12 (API 30–32): request video/audio access
        print('Requesting media permissions for Android 11–12');
        final status = await [
          Permission.videos,
          Permission.audio,
        ].request();

        _permissionsGranted =
            status[Permission.videos] == PermissionStatus.granted &&
            status[Permission.audio] == PermissionStatus.granted;

      } else {
        // Android 10 and below (API < 30): request full storage
        print('Requesting storage permission for Android < 11');
        final status = await Permission.storage.request();
        _permissionsGranted = status == PermissionStatus.granted;
      }

      print('Permissions granted: $_permissionsGranted');
      return _permissionsGranted;
    } catch (e) {
      print('Failed to request permissions: $e');
      _permissionsGranted = false;
      return false;
    }
  }

  Future<bool> checkPermissionStatus() async {
    if (!Platform.isAndroid) {
      _permissionsGranted = true;
      return true;
    }

    try {
      final sdkInt = await _getAndroidSdkInt();
      bool hasPermissions = false;

      if (sdkInt >= 33) {
        final videoStatus = await Permission.videos.status;
        final audioStatus = await Permission.audio.status;
        hasPermissions = videoStatus == PermissionStatus.granted &&
                         audioStatus == PermissionStatus.granted;
        print('Android 13+ - Videos: $videoStatus, Audio: $audioStatus');

      } else if (sdkInt >= 30) {
        final videoStatus = await Permission.videos.status;
        final audioStatus = await Permission.audio.status;
        hasPermissions = videoStatus == PermissionStatus.granted &&
                         audioStatus == PermissionStatus.granted;
        print('Android 11–12 - Videos: $videoStatus, Audio: $audioStatus');

      } else {
        final status = await Permission.storage.status;
        hasPermissions = status == PermissionStatus.granted;
        print('Android < 11 - Storage: $status');
      }

      _permissionsGranted = hasPermissions;
      return hasPermissions;
    } catch (e) {
      print('Failed to check permission status: $e');
      _permissionsGranted = false;
      return false;
    }
  }

  Future<void> openSettings() async {
    await openAppSettings();
  }

  Future<int> _getAndroidSdkInt() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.sdkInt;
    } catch (e) {
      print('Failed to get SDK version: $e');
      return 0;
    }
  }

  String getPermissionDescription() {
    if (Platform.isAndroid) {
      return 'This app needs access to your device storage or media files to save downloaded audio and video.';
    } else {
      return 'This app saves downloaded files locally to your documents folder.';
    }
  }

  Future<List<String>> getPermissionDescriptions() async {
    if (!Platform.isAndroid) {
      return ['No permissions required on this platform.'];
    }

    final sdkInt = await _getAndroidSdkInt();

    if (sdkInt >= 33) {
      return [
        'Access to video media files',
        'Access to audio media files',
      ];
    } else if (sdkInt >= 30) {
      return [
        'Access to video and audio media files (Android 11–12)',
      ];
    } else {
      return [
        'Access to device storage to save files',
      ];
    }
  }
}
