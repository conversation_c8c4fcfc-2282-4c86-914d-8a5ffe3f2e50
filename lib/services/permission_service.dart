import 'dart:io';
import 'package:permission_handler/permission_handler.dart';

class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  bool _permissionsRequested = false;
  bool _permissionsGranted = false;

  /// Check if permissions have been granted
  bool get permissionsGranted => _permissionsGranted;

  /// Request all necessary permissions on app startup
  Future<bool> requestPermissionsOnStartup() async {
    if (_permissionsRequested) {
      return _permissionsGranted;
    }

    _permissionsRequested = true;

    if (!Platform.isAndroid) {
      _permissionsGranted = true;
      return true;
    }

    try {
      // Check Android version and request appropriate permissions
      if (await _isAndroid13OrHigher()) {
        // For Android 13+ (API 33+), request media permissions
        final status = await [
          Permission.videos,
          Permission.audio,
        ].request();

        _permissionsGranted = status[Permission.videos] == PermissionStatus.granted &&
                             status[Permission.audio] == PermissionStatus.granted;
      } else {
        // For older Android versions, request storage permission
        final status = await Permission.storage.request();
        _permissionsGranted = status == PermissionStatus.granted;
      }

      return _permissionsGranted;
    } catch (e) {
      print('Failed to request permissions: $e');
      _permissionsGranted = false;
      return false;
    }
  }

  /// Check current permission status without requesting
  Future<bool> checkPermissionStatus() async {
    if (!Platform.isAndroid) {
      return true;
    }

    try {
      if (await _isAndroid13OrHigher()) {
        // For Android 13+, check media permissions
        final videoStatus = await Permission.videos.status;
        final audioStatus = await Permission.audio.status;
        return videoStatus == PermissionStatus.granted &&
               audioStatus == PermissionStatus.granted;
      } else {
        // For older Android versions, check storage permission
        final status = await Permission.storage.status;
        return status == PermissionStatus.granted;
      }
    } catch (e) {
      print('Failed to check permission status: $e');
      return false;
    }
  }

  /// Open app settings for manual permission grant
  Future<void> openSettings() async {
    await openAppSettings();
  }

  /// Checks if the device is running Android 13 or higher
  Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;

    try {
      // Check if we can access the new media permissions
      // If Permission.videos is available, we're likely on Android 13+
      await Permission.videos.status;
      return true;
    } catch (e) {
      return false; // If Permission.videos doesn't exist, we're on older Android
    }
  }

  /// Get user-friendly permission description
  String getPermissionDescription() {
    if (Platform.isAndroid) {
      return 'This app needs storage permissions to save downloaded videos and audio files to your device\'s Downloads folder.';
    } else {
      return 'This app will save downloaded files to the app\'s documents folder.';
    }
  }

  /// Get required permissions list for display
  List<String> getRequiredPermissions() {
    if (!Platform.isAndroid) {
      return ['No additional permissions required'];
    }

    return [
      'Storage access to save files',
      'Media access for video and audio files',
    ];
  }
}
