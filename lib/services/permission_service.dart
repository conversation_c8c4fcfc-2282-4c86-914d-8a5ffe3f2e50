import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  bool _permissionsRequested = false;
  bool _permissionsGranted = false;

  /// Check if permissions have been granted
  bool get permissionsGranted => _permissionsGranted;

  /// Reset permission state (useful for testing or when returning from settings)
  void resetPermissionState() {
    _permissionsRequested = false;
    _permissionsGranted = false;
  }

  /// Request all necessary permissions on app startup
  Future<bool> requestPermissionsOnStartup() async {
    if (_permissionsRequested) {
      return _permissionsGranted;
    }

    _permissionsRequested = true;

    if (!Platform.isAndroid) {
      _permissionsGranted = true;
      return true;
    }

    try {
      // Check Android version and request appropriate permissions
      final isAndroid13Plus = await _isAndroid13OrHigher();
      print('Is Android 13+: $isAndroid13Plus');

      if (isAndroid13Plus) {
        // For Android 13+ (API 33+), request media permissions
        print('Requesting media permissions for Android 13+');
        final status = await [
          Permission.videos,
          Permission.audio,
        ].request();

        _permissionsGranted = status[Permission.videos] == PermissionStatus.granted &&
                             status[Permission.audio] == PermissionStatus.granted;
        print('Media permissions granted: $_permissionsGranted');
      } else {
        // For older Android versions, request storage permission
        print('Requesting storage permission for Android 10-12');
        final status = await Permission.storage.request();
        _permissionsGranted = status == PermissionStatus.granted;
        print('Storage permission granted: $_permissionsGranted, status: $status');
      }

      return _permissionsGranted;
    } catch (e) {
      print('Failed to request permissions: $e');
      _permissionsGranted = false;
      return false;
    }
  }

  /// Check current permission status without requesting
  Future<bool> checkPermissionStatus() async {
    if (!Platform.isAndroid) {
      _permissionsGranted = true;
      return true;
    }

    try {
      bool hasPermissions = false;

      if (await _isAndroid13OrHigher()) {
        // For Android 13+, check media permissions
        final videoStatus = await Permission.videos.status;
        final audioStatus = await Permission.audio.status;
        hasPermissions = videoStatus == PermissionStatus.granted &&
                        audioStatus == PermissionStatus.granted;
        print('Android 13+ permissions - Videos: $videoStatus, Audio: $audioStatus');
      } else {
        // For older Android versions, check storage permission
        final status = await Permission.storage.status;
        hasPermissions = status == PermissionStatus.granted;
        print('Android 10 storage permission: $status');
      }

      _permissionsGranted = hasPermissions;
      return hasPermissions;
    } catch (e) {
      print('Failed to check permission status: $e');
      _permissionsGranted = false;
      return false;
    }
  }

  /// Open app settings for manual permission grant
  Future<void> openSettings() async {
    await openAppSettings();
  }

  /// Checks if the device is running Android 13 or higher
  Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;

    try {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      print('Android SDK version: $sdkInt');

      // Android 13 is API level 33
      return sdkInt >= 33;
    } catch (e) {
      print('Failed to get Android version: $e');
      // Fallback: try to check if new permissions exist
      try {
        await Permission.videos.status;
        return true;
      } catch (e2) {
        return false;
      }
    }
  }

  /// Get user-friendly permission description
  String getPermissionDescription() {
    if (Platform.isAndroid) {
      return 'This app needs storage permissions to save downloaded videos and audio files to your device\'s Downloads folder.';
    } else {
      return 'This app will save downloaded files to the app\'s documents folder.';
    }
  }

  /// Get required permissions list for display
  Future<List<String>> getRequiredPermissions() async {
    if (!Platform.isAndroid) {
      return ['No additional permissions required'];
    }

    if (await _isAndroid13OrHigher()) {
      return [
        'Media access for video files',
        'Media access for audio files',
      ];
    } else {
      return [
        'Storage access to save files to Downloads folder',
      ];
    }
  }
}
