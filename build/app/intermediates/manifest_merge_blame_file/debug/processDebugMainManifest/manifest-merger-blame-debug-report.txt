1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.blupremium.youtube_downloader"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:4:5-67
15-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:4:22-64
16    <!-- Storage permissions for Android versions below 13 (API 33) -->
17    <uses-permission
17-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:7:5-8:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:7:22-78
19        android:maxSdkVersion="32" />
19-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:8:9-35
20    <uses-permission
20-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:9:5-10:38
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:9:22-77
22        android:maxSdkVersion="32" /> <!-- For Android 11+ (API 30+) if we need to access all files -->
22-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:10:9-35
23    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- For Android 13+ (API 33+) - granular media permissions -->
23-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:13:5-14:40
23-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:13:22-79
24    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
24-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:17:5-75
24-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:17:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:18:5-75
25-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:18:22-72
26    <!--
27 Required to query activities that can process text, see:
28         https://developer.android.com/training/package-visibility and
29         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
30
31         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
32    -->
33    <queries>
33-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:58:5-63:15
34        <intent>
34-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:59:9-62:18
35            <action android:name="android.intent.action.PROCESS_TEXT" />
35-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:60:13-72
35-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:60:21-70
36
37            <data android:mimeType="text/plain" />
37-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:61:13-50
37-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:61:19-48
38        </intent>
39    </queries>
40
41    <permission
41-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
42        android:name="com.blupremium.youtube_downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
42-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
43        android:protectionLevel="signature" />
43-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
44
45    <uses-permission android:name="com.blupremium.youtube_downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
45-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
45-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
46
47    <application
48        android:name="android.app.Application"
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
50        android:debuggable="true"
51        android:extractNativeLibs="true"
52        android:icon="@mipmap/ic_launcher"
53        android:label="youtube_downloader"
54        android:requestLegacyExternalStorage="true" >
55        <activity
56            android:name="com.blupremium.youtube_downloader.MainActivity"
57            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
58            android:exported="true"
59            android:hardwareAccelerated="true"
60            android:launchMode="singleTop"
61            android:taskAffinity=""
62            android:theme="@style/LaunchTheme"
63            android:windowSoftInputMode="adjustResize" >
64
65            <!--
66                 Specifies an Android theme to apply to this Activity as soon as
67                 the Android process has started. This theme is visible to the user
68                 while the Flutter UI initializes. After that, this theme continues
69                 to determine the Window background behind the Flutter UI.
70            -->
71            <meta-data
72                android:name="io.flutter.embedding.android.NormalTheme"
73                android:resource="@style/NormalTheme" />
74
75            <intent-filter>
76                <action android:name="android.intent.action.MAIN" />
77
78                <category android:name="android.intent.category.LAUNCHER" />
79            </intent-filter>
80        </activity>
81        <!--
82             Don't delete the meta-data below.
83             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
84        -->
85        <meta-data
86            android:name="flutterEmbedding"
87            android:value="2" />
88
89        <activity
89-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
90            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
90-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
91            android:exported="false"
91-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
92            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
92-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
93
94        <uses-library
94-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
95            android:name="androidx.window.extensions"
95-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
96            android:required="false" />
96-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
97        <uses-library
97-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
98            android:name="androidx.window.sidecar"
98-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
99            android:required="false" />
99-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
100
101        <provider
101-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
102            android:name="androidx.startup.InitializationProvider"
102-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
103            android:authorities="com.blupremium.youtube_downloader.androidx-startup"
103-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
104            android:exported="false" >
104-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
105            <meta-data
105-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
106                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
106-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
107                android:value="androidx.startup" />
107-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
108            <meta-data
108-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
109                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
109-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
110                android:value="androidx.startup" />
110-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
111        </provider>
112
113        <receiver
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
114            android:name="androidx.profileinstaller.ProfileInstallReceiver"
114-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
115            android:directBootAware="false"
115-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
116            android:enabled="true"
116-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
117            android:exported="true"
117-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
118            android:permission="android.permission.DUMP" >
118-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
119            <intent-filter>
119-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
120                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
120-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
120-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
121            </intent-filter>
122            <intent-filter>
122-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
123                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
123-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
123-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
124            </intent-filter>
125            <intent-filter>
125-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
126                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
126-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
126-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
127            </intent-filter>
128            <intent-filter>
128-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
129                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
129-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
129-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
130            </intent-filter>
131        </receiver>
132    </application>
133
134</manifest>
