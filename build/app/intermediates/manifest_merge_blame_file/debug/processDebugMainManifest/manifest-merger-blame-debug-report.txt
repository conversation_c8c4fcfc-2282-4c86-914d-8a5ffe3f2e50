1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.blupremium.youtube_downloader"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:4:5-67
15-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:4:22-64
16    <!-- Storage permissions for Android versions below 13 (API 33) -->
17    <uses-permission
17-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:7:5-8:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:7:22-78
19        android:maxSdkVersion="32" />
19-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:8:9-35
20    <uses-permission
20-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:9:5-10:38
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:9:22-77
22        android:maxSdkVersion="32" /> <!-- For Android 11+ (API 30+) if we need to access all files -->
22-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:10:9-35
23    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- For Android 13+ (API 33+) - granular media permissions -->
23-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:13:5-14:40
23-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:13:22-79
24    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
24-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:17:5-75
24-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:17:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:18:5-75
25-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:18:22-72
26    <!--
27 Required to query activities that can process text, see:
28         https://developer.android.com/training/package-visibility and
29         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
30
31         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
32    -->
33    <queries>
33-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:57:5-62:15
34        <intent>
34-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:58:9-61:18
35            <action android:name="android.intent.action.PROCESS_TEXT" />
35-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:59:13-72
35-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:59:21-70
36
37            <data android:mimeType="text/plain" />
37-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:60:13-50
37-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:60:19-48
38        </intent>
39    </queries>
40
41    <permission
41-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
42        android:name="com.blupremium.youtube_downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
42-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
43        android:protectionLevel="signature" />
43-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
44
45    <uses-permission android:name="com.blupremium.youtube_downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
45-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
45-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
46
47    <application
48        android:name="android.app.Application"
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
50        android:debuggable="true"
51        android:extractNativeLibs="true"
52        android:icon="@mipmap/ic_launcher"
53        android:label="youtube_downloader" >
54        <activity
55            android:name="com.blupremium.youtube_downloader.MainActivity"
56            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
57            android:exported="true"
58            android:hardwareAccelerated="true"
59            android:launchMode="singleTop"
60            android:taskAffinity=""
61            android:theme="@style/LaunchTheme"
62            android:windowSoftInputMode="adjustResize" >
63
64            <!--
65                 Specifies an Android theme to apply to this Activity as soon as
66                 the Android process has started. This theme is visible to the user
67                 while the Flutter UI initializes. After that, this theme continues
68                 to determine the Window background behind the Flutter UI.
69            -->
70            <meta-data
71                android:name="io.flutter.embedding.android.NormalTheme"
72                android:resource="@style/NormalTheme" />
73
74            <intent-filter>
75                <action android:name="android.intent.action.MAIN" />
76
77                <category android:name="android.intent.category.LAUNCHER" />
78            </intent-filter>
79        </activity>
80        <!--
81             Don't delete the meta-data below.
82             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
83        -->
84        <meta-data
85            android:name="flutterEmbedding"
86            android:value="2" />
87
88        <activity
88-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
89            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
89-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
90            android:exported="false"
90-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
91            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
91-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
92
93        <uses-library
93-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
94            android:name="androidx.window.extensions"
94-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
95            android:required="false" />
95-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
96        <uses-library
96-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
97            android:name="androidx.window.sidecar"
97-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
98            android:required="false" />
98-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
99
100        <provider
100-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
101            android:name="androidx.startup.InitializationProvider"
101-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
102            android:authorities="com.blupremium.youtube_downloader.androidx-startup"
102-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
103            android:exported="false" >
103-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
104            <meta-data
104-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
105                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
105-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
106                android:value="androidx.startup" />
106-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
108                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
108-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
109                android:value="androidx.startup" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
110        </provider>
111
112        <receiver
112-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
113            android:name="androidx.profileinstaller.ProfileInstallReceiver"
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
114            android:directBootAware="false"
114-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
115            android:enabled="true"
115-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
116            android:exported="true"
116-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
117            android:permission="android.permission.DUMP" >
117-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
119                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
119-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
122                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
122-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
125                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
125-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
125-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
126            </intent-filter>
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
128                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
128-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
128-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
129            </intent-filter>
130        </receiver>
131    </application>
132
133</manifest>
