{"logs": [{"outputFile": "com.blupremium.youtube_downloader.app-mergeDebugResources-25:/values-az/values-az.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/res/values-az/values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,1193", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,1289"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/308a2e77faa557d0bd706972416bde6a/transformed/browser-1.8.0/res/values-az/values-az.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,255,363", "endColumns": "95,103,107,102", "endOffsets": "146,250,358,461"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "782,878,982,1090", "endColumns": "95,103,107,102", "endOffsets": "873,977,1085,1188"}}]}]}