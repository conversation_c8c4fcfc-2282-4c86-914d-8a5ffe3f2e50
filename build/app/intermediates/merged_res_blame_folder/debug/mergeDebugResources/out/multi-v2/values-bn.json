{"logs": [{"outputFile": "com.blupremium.youtube_downloader.app-mergeDebugResources-25:/values-bn/values-bn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,1207", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,1303"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/308a2e77faa557d0bd706972416bde6a/transformed/browser-1.8.0/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "784,890,992,1101", "endColumns": "105,101,108,105", "endOffsets": "885,987,1096,1202"}}]}]}