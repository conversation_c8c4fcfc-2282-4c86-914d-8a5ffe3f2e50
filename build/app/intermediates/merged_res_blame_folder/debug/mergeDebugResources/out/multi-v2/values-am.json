{"logs": [{"outputFile": "com.blupremium.youtube_downloader.app-mergeDebugResources-25:/values-am/values-am.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,1136", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,1232"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/308a2e77faa557d0bd706972416bde6a/transformed/browser-1.8.0/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "742,838,933,1039", "endColumns": "95,94,105,96", "endOffsets": "833,928,1034,1131"}}]}]}