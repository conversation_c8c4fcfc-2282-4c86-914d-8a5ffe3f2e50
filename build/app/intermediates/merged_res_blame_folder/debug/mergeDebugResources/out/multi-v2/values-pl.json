{"logs": [{"outputFile": "com.blupremium.youtube_downloader.app-mergeDebugResources-25:/values-pl/values-pl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/308a2e77faa557d0bd706972416bde6a/transformed/browser-1.8.0/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "792,892,991,1106", "endColumns": "99,98,114,103", "endOffsets": "887,986,1101,1205"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,1210", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,1306"}}]}]}