{"logs": [{"outputFile": "com.blupremium.youtube_downloader.app-mergeDebugResources-25:/values-ne/values-ne.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/308a2e77faa557d0bd706972416bde6a/transformed/browser-1.8.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,268,382", "endColumns": "100,111,113,107", "endOffsets": "151,263,377,485"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "775,876,988,1102", "endColumns": "100,111,113,107", "endOffsets": "871,983,1097,1205"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,1210", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,1306"}}]}]}