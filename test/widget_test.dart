// This is a basic Flutter widget test for the YouTube Downloader app.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:youtube_downloader/main.dart';

void main() {
  testWidgets('YouTube Downloader app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the home screen elements are present.
    expect(find.text('YouTube Downloader'), findsOneWidget);
    expect(find.text('Download YouTube Videos'), findsOneWidget);
    expect(find.text('Get Video Info'), findsOneWidget);
    expect(find.byIcon(Icons.video_library), findsOneWidget);

    // Verify that the URL input field is present.
    expect(find.byType(TextField), findsOneWidget);

    // Verify that the button is present and can be tapped.
    final button = find.text('Get Video Info');
    expect(button, findsOneWidget);

    // Test tapping the button without entering a URL (should show error).
    await tester.tap(button);
    await tester.pump();

    // The app should show a snackbar with error message.
    await tester.pump(const Duration(milliseconds: 100));
    expect(find.text('Please enter a YouTube URL'), findsOneWidget);
  });

  testWidgets('URL input field accepts text', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Find the text field and enter some text.
    final textField = find.byType(TextField);
    await tester.enterText(textField, 'https://www.youtube.com/watch?v=test');
    await tester.pump();

    // Verify that the text was entered.
    expect(find.text('https://www.youtube.com/watch?v=test'), findsOneWidget);
  });
}
